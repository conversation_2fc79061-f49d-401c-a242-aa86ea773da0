# docx2pdf

一个基于Golang的高性能文档转PDF服务，支持Word和PowerPoint文件的异步转换。采用微服务架构，支持水平扩展，提供RESTful API接口。

## ✨ 特性

- 🚀 **异步处理**: 基于Redis队列的异步任务处理，支持高并发
- 📁 **多格式支持**: 支持 .docx, .doc, .pptx, .ppt 格式文件转换
- ☁️ **云存储**: 集成阿里云OSS，自动上传转换后的PDF文件
- 🔄 **任务管理**: 完整的任务状态跟踪和错误处理
- 🛡️ **安全验证**: 文件类型和大小验证，防止恶意上传
- 📊 **健康监控**: 内置健康检查和监控接口
- 🐳 **容器化**: 支持Docker部署
- 🔧 **可配置**: 灵活的环境变量配置

## 🏗️ 架构设计

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Client    │───▶│  API Server │───▶│    Redis    │
└─────────────┘    └─────────────┘    │   Queue     │
                           │           └─────────────┘
                           ▼                   │
                   ┌─────────────┐            ▼
                   │  SQLite DB  │    ┌─────────────┐
                   │   (Tasks)   │    │   Worker    │
                   └─────────────┘    │  Processes  │
                           ▲           └─────────────┘
                           │                   │
                           └───────────────────┼───────┐
                                               ▼       ▼
                                       ┌─────────────┐ │
                                       │ LibreOffice │ │
                                       │  Converter  │ │
                                       └─────────────┘ │
                                               │       │
                                               ▼       ▼
                                       ┌─────────────┐ │
                                       │ Aliyun OSS  │ │
                                       │  Storage    │ │
                                       └─────────────┘ │
```

## 🚀 快速开始

### 环境要求

- **Go**: 1.21+
- **Redis**: 6.0+ (用于任务队列)
- **LibreOffice**: 7.0+ (用于文件转换)
- **阿里云OSS**: 存储转换后的PDF文件

### 安装依赖

#### 1. 安装Go依赖
```bash
go mod tidy
```

#### 2. 安装LibreOffice

**Ubuntu/Debian:**
```bash
sudo apt-get update
sudo apt-get install libreoffice
```

**CentOS/RHEL:**
```bash
sudo yum install libreoffice
```

**Windows:**
```bash
# 下载并安装: https://www.libreoffice.org/download/download/
```

**Docker (推荐):**
```bash
# 使用官方镜像，已包含LibreOffice
docker pull libreoffice/online
```

**安装字体**
```
2. 使用以下命令更新系统软件包列表：
   sudo apt update
3. 安装中文字体支持包：
   sudo apt install fonts-noto-cjk
   这将安装Noto字体系列，其中包含了广泛的中文字体。
4. 安装中文字体配置包：
   sudo apt install fontconfig
5. 配置中文字体：
   sudo dpkg-reconfigure fontconfig-config
   在配置过程中，选择"自动"选项以使用系统默认设置。
6. 更新字体缓存：
   sudo fc-cache -f -v
   这将更新系统中的字体缓存。
```


#### 3. 安装Redis

**Ubuntu/Debian:**
```bash
sudo apt-get install redis-server
sudo systemctl start redis-server
```

**Docker:**
```bash
docker run -d --name redis -p 6479:6479 redis:alpine
```

### 配置环境变量

创建 `.env` 文件：

```bash
cp .env.example .env
```

编辑 `.env` 文件：

```env
# 服务器配置
SERVER_PORT=48084

# Redis配置
REDIS_ADDR=localhost:6479
REDIS_PASSWORD=
REDIS_DB=0

# 阿里云OSS配置
OSS_ENDPOINT=oss-cn-hangzhou.aliyuncs.com
OSS_ACCESS_KEY_ID=your_access_key_id
OSS_ACCESS_KEY_SECRET=your_access_key_secret
OSS_BUCKET_NAME=your_bucket_name

# 文件配置
MAX_FILE_SIZE=524288000  # 500MB
TEMP_DIR=./temp

# 数据库配置
DATABASE_PATH=./docx2pdf.db
```

### 启动服务

#### 开发环境
```bash
# 启动Redis (如果未启动)
redis-server

# 启动应用
go run main.go
```

#### 生产环境
```bash
# 编译
go build -o docx2pdf

# 启动
./docx2pdf
```

#### Docker部署
```bash
# 构建镜像
docker build -t docx2pdf .

# 启动服务
docker-compose up -d
```

### API测试

#### 1. 健康检查
```bash
curl http://localhost:48084/health
```

#### 2. 上传文件转换
```bash
curl -X POST http://localhost:48084/api/v1/convert \
  -F "files=@test.docx"
```

#### 3. 查询任务状态
```bash
curl http://localhost:48084/api/v1/tasks/{task_id}
```

## 📁 项目结构

```
docx2pdf/
├── config/                 # 配置管理
│   └── config.go          # 环境变量配置
├── handlers/              # HTTP处理器
│   ├── convert.go         # 文件转换处理器
│   └── task.go           # 任务状态处理器
├── models/               # 数据模型
│   └── task.go          # 任务数据模型
├── services/            # 业务服务层
│   ├── converter.go     # 文件转换服务
│   ├── queue.go        # Redis队列服务
│   └── storage.go      # OSS存储服务
├── utils/              # 工具函数
│   └── validator.go    # 文件验证工具
├── worker/             # 异步任务处理
│   └── worker.go      # Worker进程
├── temp/              # 临时文件目录
├── main.go           # 程序入口
├── go.mod           # Go模块依赖
├── go.sum          # 依赖版本锁定
├── Dockerfile      # Docker构建文件
├── docker-compose.yml  # Docker编排文件
├── .env.example   # 环境变量示例
└── README.md     # 项目文档
```

## 📚 API 文档

### 接口概览

| 接口 | 方法 | 路径 | 描述 |
|------|------|------|------|
| 文件转换 | POST | `/api/v1/convert` | 上传文件并创建转换任务 |
| 任务状态 | GET | `/api/v1/tasks/{task_id}` | 查询任务状态和结果 |
| 任务列表 | GET | `/api/v1/tasks` | 获取任务列表 |
| 健康检查 | GET | `/health` | 服务健康状态检查 |
| 服务信息 | GET | `/` | 获取服务基本信息 |

### 1. 文件转换接口

#### 请求

```http
POST /api/v1/convert
Content-Type: multipart/form-data
```

**参数:**
- `files` (file, required): 要转换的文件，支持 .docx, .doc, .pptx, .ppt 格式

#### 响应

**成功响应 (202 Accepted):**
```json
{
    "task_id": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
    "status": "queued",
    "message": "Conversion task created successfully. Please poll /api/v1/tasks/{task_id} for status."
}
```

**错误响应:**

文件类型不支持 (400 Bad Request):
```json
{
    "error_code": "INVALID_FILE_TYPE",
    "message": "Unsupported file format for 'image.jpg'. Only .docx, .doc, .pptx, .ppt are allowed."
}
```

文件过大 (400 Bad Request):
```json
{
    "error_code": "FILE_TOO_LARGE",
    "message": "File size exceeds limit of 500MB."
}
```

### 2. 任务状态查询接口

#### 请求

```http
GET /api/v1/tasks/{task_id}
```

**路径参数:**
- `task_id` (string, required): 任务ID

#### 响应

**任务排队中 (200 OK):**
```json
{
    "task_id": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
    "status": "queued",
    "original_filename": "document.docx",
    "file_size": 1024000,
    "created_at": "2023-10-26T10:00:00Z",
    "updated_at": "2023-10-26T10:00:05Z"
}
```

**任务处理中 (200 OK):**
```json
{
    "task_id": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
    "status": "processing",
    "original_filename": "document.docx",
    "file_size": 1024000,
    "progress": 60,
    "message": "Converting document.docx to PDF...",
    "created_at": "2023-10-26T10:00:00Z",
    "updated_at": "2023-10-26T10:00:30Z"
}
```

**任务完成 (200 OK):**
```json
{
    "task_id": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
    "status": "completed",
    "original_filename": "document.docx",
    "file_size": 1024000,
    "output_url": "https://your-bucket.oss-cn-hangzhou.aliyuncs.com/pdfs/a1b2c3d4-e5f6-7890-1234-567890abcdef/document.pdf",
    "output_size": 512000,
    "created_at": "2023-10-26T10:00:00Z",
    "completed_at": "2023-10-26T10:01:00Z"
}
```

**任务失败 (200 OK):**
```json
{
    "task_id": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
    "status": "failed",
    "original_filename": "document.docx",
    "file_size": 1024000,
    "error_code": "CONVERSION_ERROR",
    "error_message": "Document is corrupted or unreadable",
    "created_at": "2023-10-26T10:00:00Z",
    "updated_at": "2023-10-26T10:00:45Z"
}
```

**任务不存在 (404 Not Found):**
```json
{
    "error_code": "TASK_NOT_FOUND",
    "message": "Task not found"
}
```

### 3. 健康检查接口

#### 请求

```http
GET /health
```

#### 响应

**服务正常 (200 OK):**
```json
{
    "status": "healthy",
    "queue_length": 5,
    "timestamp": "2023-10-26T10:00:00Z"
}
```

**服务异常 (500 Internal Server Error):**
```json
{
    "status": "unhealthy",
    "error": "Redis connection failed"
}
```

### 任务状态说明

| 状态 | 描述 |
|------|------|
| `queued` | 任务已创建，等待处理 |
| `processing` | 任务正在处理中 |
| `completed` | 任务处理完成 |
| `failed` | 任务处理失败 |

### 错误码说明

| 错误码 | 描述 |
|--------|------|
| `MISSING_FILE` | 未上传文件 |
| `INVALID_FILE_TYPE` | 不支持的文件类型 |
| `FILE_TOO_LARGE` | 文件大小超出限制 |
| `VALIDATION_ERROR` | 文件验证失败 |
| `TASK_NOT_FOUND` | 任务不存在 |
| `CONVERSION_ERROR` | 文件转换失败 |
| `STORAGE_ERROR` | 存储服务错误 |
| `QUEUE_ERROR` | 队列服务错误 |

## 🔧 配置说明

### 环境变量配置

| 变量名 | 描述 | 默认值 | 必填 |
|--------|------|--------|------|
| `SERVER_PORT` | 服务端口 | `48084` | 否 |
| `REDIS_ADDR` | Redis地址 | `localhost:6479` | 否 |
| `REDIS_PASSWORD` | Redis密码 | `` | 否 |
| `REDIS_DB` | Redis数据库 | `0` | 否 |
| `OSS_ENDPOINT` | 阿里云OSS端点 | `` | 是 |
| `OSS_ACCESS_KEY_ID` | 阿里云访问密钥ID | `` | 是 |
| `OSS_ACCESS_KEY_SECRET` | 阿里云访问密钥Secret | `` | 是 |
| `OSS_BUCKET_NAME` | OSS存储桶名称 | `` | 是 |
| `MAX_FILE_SIZE` | 最大文件大小(字节) | `524288000` (500MB) | 否 |
| `TEMP_DIR` | 临时文件目录 | `./temp` | 否 |
| `DATABASE_PATH` | SQLite数据库路径 | `./docx2pdf.db` | 否 |

### 配置示例

创建 `.env` 文件：

```env
# 服务器配置
SERVER_PORT=48084

# Redis配置
REDIS_ADDR=localhost:6479
REDIS_PASSWORD=
REDIS_DB=0

# 阿里云OSS配置
OSS_ENDPOINT=oss-cn-hangzhou.aliyuncs.com
OSS_ACCESS_KEY_ID=LTAI5tXXXXXXXXXXXXXX
OSS_ACCESS_KEY_SECRET=XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
OSS_BUCKET_NAME=my-docx2pdf-bucket

# 文件配置
MAX_FILE_SIZE=524288000  # 500MB
TEMP_DIR=./temp

# 数据库配置
DATABASE_PATH=./docx2pdf.db
```

## 🐳 Docker 部署

### 使用 Docker Compose (推荐)

1. **创建 docker-compose.yml 文件:**

```yaml
version: '3.8'

services:
  docx2pdf:
    build: .
    ports:
      - "48084:48084"
    environment:
      - REDIS_ADDR=redis:6479
      - OSS_ENDPOINT=${OSS_ENDPOINT}
      - OSS_ACCESS_KEY_ID=${OSS_ACCESS_KEY_ID}
      - OSS_ACCESS_KEY_SECRET=${OSS_ACCESS_KEY_SECRET}
      - OSS_BUCKET_NAME=${OSS_BUCKET_NAME}
    volumes:
      - ./temp:/app/temp
      - ./data:/app/data
    depends_on:
      - redis
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6479:6479"
    volumes:
      - redis_data:/data
    restart: unless-stopped

volumes:
  redis_data:
```

2. **创建 Dockerfile:**

```dockerfile
FROM golang:1.21-alpine AS builder

# 安装构建依赖
RUN apk add --no-cache git

# 设置工作目录
WORKDIR /app

# 复制 go mod 文件
COPY go.mod go.sum ./

# 下载依赖
RUN go mod download

# 复制源代码
COPY . .

# 构建应用
RUN CGO_ENABLED=1 GOOS=linux go build -a -installsuffix cgo -o main .

# 运行阶段
FROM alpine:latest

# 安装运行时依赖
RUN apk add --no-cache \
    libreoffice \
    ca-certificates \
    tzdata \
    && rm -rf /var/cache/apk/*

# 设置时区
ENV TZ=Asia/Shanghai

# 创建应用用户
RUN addgroup -g 1001 -S appgroup && \
    adduser -u 1001 -S appuser -G appgroup

# 设置工作目录
WORKDIR /app

# 从构建阶段复制二进制文件
COPY --from=builder /app/main .

# 创建必要的目录
RUN mkdir -p temp data && \
    chown -R appuser:appgroup /app

# 切换到应用用户
USER appuser

# 暴露端口
EXPOSE 48084

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:48084/health || exit 1

# 启动应用
CMD ["./main"]
```

3. **启动服务:**

```bash
# 启动所有服务
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

### 单独使用 Docker

```bash
# 构建镜像
docker build -t docx2pdf .

# 启动 Redis
docker run -d --name redis -p 6479:6479 redis:alpine

# 启动应用
docker run -d \
  --name docx2pdf \
  -p 48084:48084 \
  -e REDIS_ADDR=redis:6479 \
  -e OSS_ENDPOINT=your-oss-endpoint \
  -e OSS_ACCESS_KEY_ID=your-access-key \
  -e OSS_ACCESS_KEY_SECRET=your-secret-key \
  -e OSS_BUCKET_NAME=your-bucket \
  --link redis:redis \
  docx2pdf
```

## 🛠️ 开发指南

### 本地开发环境搭建

1. **克隆项目:**

```bash
git clone <repository-url>
cd docx2pdf
```

2. **安装依赖:**

```bash
# 安装 Go 依赖
go mod tidy

# 安装 LibreOffice
# Ubuntu/Debian
sudo apt-get install libreoffice

# macOS
brew install --cask libreoffice

# Windows
# 下载安装包: https://www.libreoffice.org/download/
```

3. **启动 Redis:**

```bash
# 使用 Docker
docker run -d --name redis -p 6479:6479 redis:alpine

# 或者本地安装
redis-server
```

4. **配置环境变量:**

```bash
cp .env.example .env
# 编辑 .env 文件，填入正确的配置
```

5. **运行应用:**

```bash
go run main.go
```

### 代码结构说明

```
docx2pdf/
├── config/          # 配置管理
│   └── config.go   # 环境变量加载和配置结构
├── handlers/        # HTTP 处理器
│   ├── convert.go  # 文件转换接口处理
│   └── task.go     # 任务状态查询处理
├── models/          # 数据模型
│   └── task.go     # 任务数据模型定义
├── services/        # 业务服务层
│   ├── converter.go # 文件转换服务
│   ├── queue.go    # Redis 队列服务
│   └── storage.go  # OSS 存储服务
├── utils/           # 工具函数
│   └── validator.go # 文件验证工具
├── worker/          # 异步任务处理
│   └── worker.go   # Worker 进程实现
└── main.go         # 应用入口点
```

### 添加新功能

1. **添加新的 API 接口:**
   - 在 `handlers/` 目录下创建新的处理器
   - 在 `main.go` 中注册路由
   - 更新 API 文档

2. **添加新的服务:**
   - 在 `services/` 目录下创建新的服务文件
   - 实现服务接口
   - 在需要的地方注入服务

3. **修改数据模型:**
   - 更新 `models/` 目录下的模型定义
   - 运行数据库迁移（如果需要）

### 测试

```bash
# 运行所有测试
go test ./...

# 运行特定包的测试
go test ./handlers

# 运行测试并显示覆盖率
go test -cover ./...

# 生成测试覆盖率报告
go test -coverprofile=coverage.out ./...
go tool cover -html=coverage.out
```

## 📊 性能优化

### 系统性能建议

1. **Worker 进程数量调优:**
   - 根据 CPU 核心数和转换任务复杂度调整 Worker 数量
   - 建议设置为 CPU 核心数的 1-2 倍

2. **Redis 配置优化:**
   ```redis
   # redis.conf
   maxmemory 2gb
   maxmemory-policy allkeys-lru
   save 900 1
   save 300 10
   save 60 10000
   ```

3. **文件存储优化:**
   - 使用 SSD 存储临时文件
   - 定期清理过期的临时文件
   - 配置 OSS 的 CDN 加速

4. **数据库优化:**
   ```sql
   -- 为任务表添加索引
   CREATE INDEX idx_task_status ON tasks(status);
   CREATE INDEX idx_task_created_at ON tasks(created_at);
   ```

### 监控指标

建议监控以下关键指标：

- **系统指标:**
  - CPU 使用率
  - 内存使用率
  - 磁盘 I/O
  - 网络 I/O

- **应用指标:**
  - 队列长度
  - 任务处理时间
  - 成功/失败率
  - 并发连接数

- **业务指标:**
  - 每小时转换任务数
  - 平均文件大小
  - 转换成功率

## 🔍 故障排除

### 常见问题

#### 1. LibreOffice 转换失败

**问题:** 转换任务状态显示失败，错误信息包含 LibreOffice 相关内容

**解决方案:**
```bash
# 检查 LibreOffice 是否正确安装
libreoffice --version

# 检查 LibreOffice 是否可以无头模式运行
libreoffice --headless --convert-to pdf test.docx

# 重新安装 LibreOffice
sudo apt-get remove --purge libreoffice*
sudo apt-get install libreoffice
```

#### 2. Redis 连接失败

**问题:** 健康检查失败，显示 Redis 连接错误

**解决方案:**
```bash
# 检查 Redis 服务状态
redis-cli ping

# 检查 Redis 配置
redis-cli config get "*"

# 重启 Redis 服务
sudo systemctl restart redis-server
```

#### 3. OSS 上传失败

**问题:** 文件转换成功但上传到 OSS 失败

**解决方案:**
1. 检查 OSS 配置是否正确
2. 验证访问密钥权限
3. 检查网络连接
4. 确认存储桶存在且有写权限

#### 4. 内存不足

**问题:** 处理大文件时出现内存不足错误

**解决方案:**
```bash
# 增加系统内存
# 或者调整文件大小限制
export MAX_FILE_SIZE=100000000  # 100MB

# 优化 Go 垃圾回收
export GOGC=100
```

### 日志分析

#### 启用详细日志

```go
// 在 main.go 中设置日志级别
logrus.SetLevel(logrus.DebugLevel)
```

#### 常用日志查看命令

```bash
# 查看应用日志
tail -f /var/log/docx2pdf/app.log

# 查看错误日志
grep "ERROR" /var/log/docx2pdf/app.log

# 查看特定任务的日志
grep "task_id:xxx" /var/log/docx2pdf/app.log
```

## 🚀 生产部署

### 系统要求

**最低配置:**
- CPU: 2 核
- 内存: 4GB
- 存储: 20GB SSD
- 网络: 100Mbps

**推荐配置:**
- CPU: 4 核
- 内存: 8GB
- 存储: 50GB SSD
- 网络: 1Gbps

### 部署检查清单

- [ ] 环境变量配置正确
- [ ] Redis 服务正常运行
- [ ] LibreOffice 正确安装
- [ ] OSS 存储桶配置正确
- [ ] 防火墙规则配置
- [ ] SSL 证书配置（如需要）
- [ ] 监控系统配置
- [ ] 日志轮转配置
- [ ] 备份策略制定

### 安全建议

1. **网络安全:**
   - 使用防火墙限制访问
   - 配置 HTTPS
   - 使用 VPN 或内网访问

2. **应用安全:**
   - 定期更新依赖
   - 使用非 root 用户运行
   - 限制文件上传大小

3. **数据安全:**
   - 定期备份数据库
   - 加密敏感配置
   - 设置访问日志

## 🤝 贡献指南

### 提交代码

1. Fork 项目
2. 创建功能分支: `git checkout -b feature/new-feature`
3. 提交更改: `git commit -am 'Add new feature'`
4. 推送分支: `git push origin feature/new-feature`
5. 提交 Pull Request

### 代码规范

- 遵循 Go 官方代码规范
- 使用 `gofmt` 格式化代码
- 添加必要的注释
- 编写单元测试

### 问题报告

提交 Issue 时请包含：
- 详细的问题描述
- 复现步骤
- 环境信息
- 相关日志

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

- 项目主页: [GitHub Repository]
- 问题反馈: [GitHub Issues]
- 邮箱: [<EMAIL>]

---

**感谢使用 docx2pdf！** 🎉
