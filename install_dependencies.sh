#!/bin/bash

# 文档转图片依赖安装脚本
# 支持 Ubuntu/Debian 和 CentOS/RHEL 系统

set -e

echo "=== 文档转图片服务依赖安装脚本 ==="
echo "此脚本将安装以下依赖："
echo "1. LibreOffice (文档转换)"
echo "2. ImageMagick 或 poppler-utils (PDF转图片)"
echo "3. 必要的字体支持"
echo ""

# 检测操作系统
if [ -f /etc/os-release ]; then
    . /etc/os-release
    OS=$NAME
    VER=$VERSION_ID
elif type lsb_release >/dev/null 2>&1; then
    OS=$(lsb_release -si)
    VER=$(lsb_release -sr)
elif [ -f /etc/lsb-release ]; then
    . /etc/lsb-release
    OS=$DISTRIB_ID
    VER=$DISTRIB_RELEASE
elif [ -f /etc/debian_version ]; then
    OS=Debian
    VER=$(cat /etc/debian_version)
elif [ -f /etc/SuSe-release ]; then
    OS=openSUSE
elif [ -f /etc/redhat-release ]; then
    OS=RedHat
else
    OS=$(uname -s)
    VER=$(uname -r)
fi

echo "检测到操作系统: $OS $VER"
echo ""

# 安装函数
install_ubuntu_debian() {
    echo "=== 在 Ubuntu/Debian 系统上安装依赖 ==="
    
    # 更新包列表
    echo "更新包列表..."
    sudo apt-get update
    
    # 安装 LibreOffice
    echo "安装 LibreOffice..."
    sudo apt-get install -y libreoffice
    
    # 安装 ImageMagick (优先选择)
    echo "安装 ImageMagick..."
    sudo apt-get install -y imagemagick
    
    # 安装 poppler-utils (备选方案)
    echo "安装 poppler-utils..."
    sudo apt-get install -y poppler-utils
    
    # 安装字体支持
    echo "安装字体支持..."
    sudo apt-get install -y fonts-wqy-microhei fonts-wqy-zenhei
    sudo apt-get install -y fonts-liberation fonts-dejavu-core
    
    # 更新字体缓存
    echo "更新字体缓存..."
    sudo fc-cache -fv
    
    echo "Ubuntu/Debian 依赖安装完成！"
}

install_centos_rhel() {
    echo "=== 在 CentOS/RHEL 系统上安装依赖 ==="
    
    # 安装 EPEL 仓库（如果需要）
    if ! rpm -qa | grep -q epel-release; then
        echo "安装 EPEL 仓库..."
        sudo yum install -y epel-release
    fi
    
    # 安装 LibreOffice
    echo "安装 LibreOffice..."
    sudo yum install -y libreoffice
    
    # 安装 ImageMagick
    echo "安装 ImageMagick..."
    sudo yum install -y ImageMagick
    
    # 安装 poppler-utils
    echo "安装 poppler-utils..."
    sudo yum install -y poppler-utils
    
    # 安装字体支持
    echo "安装字体支持..."
    sudo yum install -y wqy-microhei-fonts wqy-zenhei-fonts
    sudo yum install -y liberation-fonts dejavu-fonts-common
    
    # 更新字体缓存
    echo "更新字体缓存..."
    sudo fc-cache -fv
    
    echo "CentOS/RHEL 依赖安装完成！"
}

install_fedora() {
    echo "=== 在 Fedora 系统上安装依赖 ==="
    
    # 安装 LibreOffice
    echo "安装 LibreOffice..."
    sudo dnf install -y libreoffice
    
    # 安装 ImageMagick
    echo "安装 ImageMagick..."
    sudo dnf install -y ImageMagick
    
    # 安装 poppler-utils
    echo "安装 poppler-utils..."
    sudo dnf install -y poppler-utils
    
    # 安装字体支持
    echo "安装字体支持..."
    sudo dnf install -y google-noto-cjk-fonts
    sudo dnf install -y liberation-fonts dejavu-fonts-common
    
    # 更新字体缓存
    echo "更新字体缓存..."
    sudo fc-cache -fv
    
    echo "Fedora 依赖安装完成！"
}

# 根据操作系统选择安装方法
case $OS in
    "Ubuntu"* | "Debian"*)
        install_ubuntu_debian
        ;;
    "CentOS"* | "Red Hat"* | "RedHat"*)
        install_centos_rhel
        ;;
    "Fedora"*)
        install_fedora
        ;;
    *)
        echo "不支持的操作系统: $OS"
        echo "请手动安装以下依赖："
        echo "1. LibreOffice"
        echo "2. ImageMagick 或 poppler-utils"
        echo "3. 中文字体支持"
        exit 1
        ;;
esac

echo ""
echo "=== 验证安装 ==="

# 验证 LibreOffice
if command -v libreoffice >/dev/null 2>&1; then
    echo "✓ LibreOffice 安装成功: $(libreoffice --version)"
else
    echo "✗ LibreOffice 安装失败"
fi

# 验证 ImageMagick
if command -v magick >/dev/null 2>&1; then
    echo "✓ ImageMagick 安装成功: $(magick --version | head -n1)"
elif command -v convert >/dev/null 2>&1; then
    echo "✓ ImageMagick 安装成功: $(convert --version | head -n1)"
else
    echo "⚠ ImageMagick 未找到，将尝试使用 pdftoppm"
fi

# 验证 poppler-utils
if command -v pdftoppm >/dev/null 2>&1; then
    echo "✓ poppler-utils 安装成功: $(pdftoppm -v 2>&1 | head -n1)"
else
    echo "⚠ poppler-utils 未找到"
fi

echo ""
echo "=== 安装完成 ==="
echo "现在可以使用文档转图片功能了！"
echo ""
echo "使用示例："
echo "curl -X POST http://localhost:48084/api/v1/convert \\"
echo "  -F \"files=@document.docx\" \\"
echo "  -F \"outType=jpg\""
