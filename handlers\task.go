package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"

	"docx2pdf/models"
)

type TaskHandler struct {
	db *gorm.DB
}

func NewTaskHandler(db *gorm.DB) *TaskHandler {
	return &TaskHandler{db: db}
}

// GetTaskStatus 获取任务状态
func (h *TaskHandler) GetTaskStatus(c *gin.Context) {
	taskID := c.Param("task_id")
	
	if taskID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error_code": "MISSING_TASK_ID",
			"message":    "Task ID is required",
		})
		return
	}
	
	// 从数据库查询任务
	var task models.Task
	err := h.db.Where("task_id = ?", taskID).First(&task).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.<PERSON>(http.StatusNotFound, gin.H{
				"error_code": "TASK_NOT_FOUND",
				"message":    "Task not found",
			})
		} else {
			logrus.Errorf("Failed to query task %s: %v", taskID, err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"error_code": "DATABASE_ERROR",
				"message":    "Failed to query task status",
			})
		}
		return
	}
	
	// 返回任务状态
	response := task.ToResponse()
	c.JSON(http.StatusOK, response)
}

// ListTasks 获取任务列表（可选功能）
func (h *TaskHandler) ListTasks(c *gin.Context) {
	// 分页参数
	page := 1
	pageSize := 20
	
	if p := c.Query("page"); p != "" {
		if parsed, err := parsePositiveInt(p); err == nil && parsed > 0 {
			page = parsed
		}
	}
	
	if ps := c.Query("page_size"); ps != "" {
		if parsed, err := parsePositiveInt(ps); err == nil && parsed > 0 && parsed <= 100 {
			pageSize = parsed
		}
	}
	
	// 状态过滤
	status := c.Query("status")
	
	// 构建查询
	query := h.db.Model(&models.Task{})
	if status != "" {
		query = query.Where("status = ?", status)
	}
	
	// 计算总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		logrus.Errorf("Failed to count tasks: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error_code": "DATABASE_ERROR",
			"message":    "Failed to count tasks",
		})
		return
	}
	
	// 查询任务列表
	var tasks []models.Task
	offset := (page - 1) * pageSize
	err := query.Order("created_at DESC").
		Limit(pageSize).
		Offset(offset).
		Find(&tasks).Error
	
	if err != nil {
		logrus.Errorf("Failed to query tasks: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error_code": "DATABASE_ERROR",
			"message":    "Failed to query tasks",
		})
		return
	}
	
	// 转换为响应格式
	var responses []models.TaskResponse
	for _, task := range tasks {
		responses = append(responses, task.ToResponse())
	}
	
	c.JSON(http.StatusOK, gin.H{
		"tasks": responses,
		"pagination": gin.H{
			"page":       page,
			"page_size":  pageSize,
			"total":      total,
			"total_pages": (total + int64(pageSize) - 1) / int64(pageSize),
		},
	})
}

// parsePositiveInt 解析正整数
func parsePositiveInt(s string) (int, error) {
	var result int
	for _, r := range s {
		if r < '0' || r > '9' {
			return 0, nil
		}
		result = result*10 + int(r-'0')
	}
	return result, nil
}
