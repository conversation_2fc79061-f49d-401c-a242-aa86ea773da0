package config

import (
	"os"
	"strconv"

	"github.com/joho/godotenv"
	"github.com/sirupsen/logrus"
)

type Config struct {
	// Server配置
	ServerPort string

	// Redis配置
	RedisAddr     string
	RedisPassword string
	RedisDB       int

	// 阿里云OSS配置
	OSSEndpoint        string
	OSSAccessKeyID     string
	OSSAccessKeySecret string
	OSSBucketName      string

	// 文件配置
	MaxFileSize      int64  // 单个文件最大大小(字节)
	TempDir          string // 临时文件目录
	AllowedFileTypes []string

	// 数据库配置
	DatabasePath string
}

var AppConfig *Config

func LoadConfig() error {
	// 加载.env文件
	if err := godotenv.Load(); err != nil {
		logrus.Warn("No .env file found, using environment variables")
	}

	AppConfig = &Config{
		ServerPort:    getEnv("SERVER_PORT", "48084"),
		RedisAddr:     getEnv("REDIS_ADDR", "localhost:6479"),
		RedisPassword: getEnv("REDIS_PASSWORD", ""),
		RedisDB:       getEnvAsInt("REDIS_DB", 0),

		OSSEndpoint:        getEnv("OSS_ENDPOINT", ""),
		OSSAccessKeyID:     getEnv("OSS_ACCESS_KEY_ID", ""),
		OSSAccessKeySecret: getEnv("OSS_ACCESS_KEY_SECRET", ""),
		OSSBucketName:      getEnv("OSS_BUCKET_NAME", ""),

		MaxFileSize:      getEnvAsInt64("MAX_FILE_SIZE", 500*1024*1024), // 默认500MB
		TempDir:          getEnv("TEMP_DIR", "./temp"),
		AllowedFileTypes: []string{".docx", ".doc", ".pptx", ".ppt"},

		DatabasePath: getEnv("DATABASE_PATH", "./docx2pdf.db"),
	}

	return nil
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvAsInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

func getEnvAsInt64(key string, defaultValue int64) int64 {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.ParseInt(value, 10, 64); err == nil {
			return intValue
		}
	}
	return defaultValue
}
