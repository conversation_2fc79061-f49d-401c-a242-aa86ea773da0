# API 使用示例

本文档提供了 docx2pdf 服务的详细 API 使用示例。

## 基础用法

### 1. 上传文件并转换

```bash
# 使用 curl 上传文件
curl -X POST http://localhost:48084/api/v1/convert \
  -F "files=@document.docx" \
  -H "Content-Type: multipart/form-data"
```

**响应示例:**
```json
{
    "message": "Conversion task created successfully. Please poll /api/v1/tasks/8a7c61c0-e0f3-4dad-a53b-b5170f6f6ada for status.",
    "status": "queued",
    "task_id": "8a7c61c0-e0f3-4dad-a53b-b5170f6f6ada"
}
```

### 2. 查询任务状态

```bash
# 查询任务状态
curl http://localhost:48084/api/v1/tasks/a1b2c3d4-e5f6-7890-1234-567890abcdef
```

**响应示例 (处理中):**
```json
{
    "task_id": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
    "status": "processing",
    "original_filename": "document.docx",
    "file_size": 1024000,
    "progress": 60,
    "message": "Converting document.docx to PDF...",
    "created_at": "2023-10-26T10:00:00Z",
    "updated_at": "2023-10-26T10:00:30Z"
}
```

**响应示例 (完成):**
```json
{
    "task_id": "8a7c61c0-e0f3-4dad-a53b-b5170f6f6ada",
    "status": "completed",
    "progress": 100,
    "message": "Conversion completed successfully",
    "output_files": [
        {
            "original_filename": "地貌景观的主要特点.docx",
            "converted_filename": "地貌景观的主要特点.pdf",
            "url": "https://corling-test.oss-cn-shanghai.aliyuncs.com/pdfs/8a7c61c0-e0f3-4dad-a53b-b5170f6f6ada/地貌景观的主要特点.pdf",
            "size_bytes": 501106
        }
    ],
    "created_at": "2025-06-04T10:43:14.099452267+08:00",
    "updated_at": "2025-06-04T10:43:15.507202845+08:00",
    "completed_at": "2025-06-04T10:43:15.507062072+08:00"
}
```

## 编程语言示例

### JavaScript (Node.js)

```javascript
const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');

async function convertFile(filePath) {
    try {
        // 1. 上传文件
        const form = new FormData();
        form.append('files', fs.createReadStream(filePath));
        
        const uploadResponse = await axios.post(
            'http://localhost:48084/api/v1/convert',
            form,
            {
                headers: {
                    ...form.getHeaders(),
                },
            }
        );
        
        const taskId = uploadResponse.data.task_id;
        console.log('Task created:', taskId);
        
        // 2. 轮询任务状态
        let status = 'queued';
        while (status === 'queued' || status === 'processing') {
            await new Promise(resolve => setTimeout(resolve, 2000)); // 等待2秒
            
            const statusResponse = await axios.get(
                `http://localhost:48084/api/v1/tasks/${taskId}`
            );
            
            status = statusResponse.data.status;
            console.log('Task status:', status);
            
            if (status === 'completed') {
                console.log('Download URL:', statusResponse.data.output_url);
                return statusResponse.data.output_url;
            } else if (status === 'failed') {
                throw new Error(statusResponse.data.error_message);
            }
        }
    } catch (error) {
        console.error('Error:', error.message);
        throw error;
    }
}

// 使用示例
convertFile('./document.docx')
    .then(url => console.log('Conversion completed:', url))
    .catch(err => console.error('Conversion failed:', err));
```

### Python

```python
import requests
import time
import os

def convert_file(file_path):
    """
    转换文件为PDF
    
    Args:
        file_path (str): 要转换的文件路径
        
    Returns:
        str: PDF文件的下载URL
    """
    base_url = 'http://localhost:48084'
    
    try:
        # 1. 上传文件
        with open(file_path, 'rb') as f:
            files = {'files': f}
            response = requests.post(f'{base_url}/api/v1/convert', files=files)
            response.raise_for_status()
            
        task_data = response.json()
        task_id = task_data['task_id']
        print(f'Task created: {task_id}')
        
        # 2. 轮询任务状态
        while True:
            time.sleep(2)  # 等待2秒
            
            response = requests.get(f'{base_url}/api/v1/tasks/{task_id}')
            response.raise_for_status()
            
            task_status = response.json()
            status = task_status['status']
            print(f'Task status: {status}')
            
            if status == 'completed':
                print(f'Download URL: {task_status["output_url"]}')
                return task_status['output_url']
            elif status == 'failed':
                raise Exception(task_status.get('error_message', 'Conversion failed'))
            elif status not in ['queued', 'processing']:
                raise Exception(f'Unknown status: {status}')
                
    except requests.exceptions.RequestException as e:
        print(f'Request error: {e}')
        raise
    except Exception as e:
        print(f'Error: {e}')
        raise

# 使用示例
if __name__ == '__main__':
    try:
        url = convert_file('./document.docx')
        print(f'Conversion completed: {url}')
    except Exception as e:
        print(f'Conversion failed: {e}')
```

### Go

```go
package main

import (
    "bytes"
    "encoding/json"
    "fmt"
    "io"
    "mime/multipart"
    "net/http"
    "os"
    "path/filepath"
    "time"
)

type TaskResponse struct {
    TaskID    string `json:"task_id"`
    Status    string `json:"status"`
    OutputURL string `json:"output_url"`
    Message   string `json:"message"`
    ErrorMessage string `json:"error_message"`
}

func convertFile(filePath string) (string, error) {
    baseURL := "http://localhost:48084"
    
    // 1. 上传文件
    taskID, err := uploadFile(baseURL, filePath)
    if err != nil {
        return "", fmt.Errorf("upload failed: %v", err)
    }
    
    fmt.Printf("Task created: %s\n", taskID)
    
    // 2. 轮询任务状态
    for {
        time.Sleep(2 * time.Second)
        
        task, err := getTaskStatus(baseURL, taskID)
        if err != nil {
            return "", fmt.Errorf("get status failed: %v", err)
        }
        
        fmt.Printf("Task status: %s\n", task.Status)
        
        switch task.Status {
        case "completed":
            fmt.Printf("Download URL: %s\n", task.OutputURL)
            return task.OutputURL, nil
        case "failed":
            return "", fmt.Errorf("conversion failed: %s", task.ErrorMessage)
        case "queued", "processing":
            continue
        default:
            return "", fmt.Errorf("unknown status: %s", task.Status)
        }
    }
}

func uploadFile(baseURL, filePath string) (string, error) {
    file, err := os.Open(filePath)
    if err != nil {
        return "", err
    }
    defer file.Close()
    
    var buf bytes.Buffer
    writer := multipart.NewWriter(&buf)
    
    part, err := writer.CreateFormFile("files", filepath.Base(filePath))
    if err != nil {
        return "", err
    }
    
    _, err = io.Copy(part, file)
    if err != nil {
        return "", err
    }
    
    err = writer.Close()
    if err != nil {
        return "", err
    }
    
    req, err := http.NewRequest("POST", baseURL+"/api/v1/convert", &buf)
    if err != nil {
        return "", err
    }
    
    req.Header.Set("Content-Type", writer.FormDataContentType())
    
    client := &http.Client{}
    resp, err := client.Do(req)
    if err != nil {
        return "", err
    }
    defer resp.Body.Close()
    
    if resp.StatusCode != http.StatusAccepted {
        return "", fmt.Errorf("upload failed with status: %d", resp.StatusCode)
    }
    
    var task TaskResponse
    err = json.NewDecoder(resp.Body).Decode(&task)
    if err != nil {
        return "", err
    }
    
    return task.TaskID, nil
}

func getTaskStatus(baseURL, taskID string) (*TaskResponse, error) {
    resp, err := http.Get(fmt.Sprintf("%s/api/v1/tasks/%s", baseURL, taskID))
    if err != nil {
        return nil, err
    }
    defer resp.Body.Close()
    
    if resp.StatusCode != http.StatusOK {
        return nil, fmt.Errorf("get status failed with status: %d", resp.StatusCode)
    }
    
    var task TaskResponse
    err = json.NewDecoder(resp.Body).Decode(&task)
    if err != nil {
        return nil, err
    }
    
    return &task, nil
}

func main() {
    url, err := convertFile("./document.docx")
    if err != nil {
        fmt.Printf("Conversion failed: %v\n", err)
        return
    }
    
    fmt.Printf("Conversion completed: %s\n", url)
}
```

## 错误处理

### 常见错误响应

1. **文件类型不支持**
```json
{
    "error_code": "INVALID_FILE_TYPE",
    "message": "Unsupported file format for 'image.jpg'. Only .docx, .doc, .pptx, .ppt are allowed."
}
```

2. **文件过大**
```json
{
    "error_code": "FILE_TOO_LARGE",
    "message": "File size exceeds limit of 500MB."
}
```

3. **任务不存在**
```json
{
    "error_code": "TASK_NOT_FOUND",
    "message": "Task not found"
}
```

### 重试机制建议

```javascript
async function convertFileWithRetry(filePath, maxRetries = 3) {
    for (let i = 0; i < maxRetries; i++) {
        try {
            return await convertFile(filePath);
        } catch (error) {
            console.log(`Attempt ${i + 1} failed:`, error.message);
            
            if (i === maxRetries - 1) {
                throw error; // 最后一次重试失败，抛出错误
            }
            
            // 指数退避
            await new Promise(resolve => 
                setTimeout(resolve, Math.pow(2, i) * 1000)
            );
        }
    }
}
```

## 批量处理示例

```python
import asyncio
import aiohttp
import aiofiles

async def convert_files_batch(file_paths, max_concurrent=5):
    """
    批量转换文件
    
    Args:
        file_paths (list): 文件路径列表
        max_concurrent (int): 最大并发数
        
    Returns:
        list: 转换结果列表
    """
    semaphore = asyncio.Semaphore(max_concurrent)
    
    async def convert_single_file(session, file_path):
        async with semaphore:
            return await convert_file_async(session, file_path)
    
    async with aiohttp.ClientSession() as session:
        tasks = [convert_single_file(session, path) for path in file_paths]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
    return results

async def convert_file_async(session, file_path):
    """异步转换单个文件"""
    base_url = 'http://localhost:48084'
    
    # 上传文件
    async with aiofiles.open(file_path, 'rb') as f:
        data = aiohttp.FormData()
        data.add_field('files', await f.read(), filename=file_path)
        
        async with session.post(f'{base_url}/api/v1/convert', data=data) as resp:
            task_data = await resp.json()
            task_id = task_data['task_id']
    
    # 轮询状态
    while True:
        await asyncio.sleep(2)
        
        async with session.get(f'{base_url}/api/v1/tasks/{task_id}') as resp:
            task_status = await resp.json()
            
        if task_status['status'] == 'completed':
            return task_status['output_url']
        elif task_status['status'] == 'failed':
            raise Exception(task_status.get('error_message', 'Conversion failed'))

# 使用示例
if __name__ == '__main__':
    files = ['doc1.docx', 'doc2.pptx', 'doc3.doc']
    results = asyncio.run(convert_files_batch(files))
    
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            print(f'File {files[i]} failed: {result}')
        else:
            print(f'File {files[i]} completed: {result}')
```
