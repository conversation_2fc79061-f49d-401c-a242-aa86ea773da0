package models

import (
	"encoding/json"
	"fmt"
	"path/filepath"
	"strings"
	"time"

	"gorm.io/gorm"
)

// TaskStatus 任务状态枚举
type TaskStatus string

const (
	StatusQueued     TaskStatus = "queued"
	StatusProcessing TaskStatus = "processing"
	StatusCompleted  TaskStatus = "completed"
	StatusFailed     TaskStatus = "failed"
)

// Task 转换任务模型
type Task struct {
	ID          uint           `json:"-" gorm:"primarykey"`
	TaskID      string         `json:"task_id" gorm:"uniqueIndex;not null"`
	Status      TaskStatus     `json:"status" gorm:"not null;default:'queued'"`
	Progress    int            `json:"progress" gorm:"default:0"`
	Message     string         `json:"message,omitempty"`
	ErrorCode   string         `json:"error_code,omitempty"`

	// 文件信息
	OriginalFilename string `json:"original_filename,omitempty"`
	ConvertedFilename string `json:"converted_filename,omitempty"`
	FileSize         int64  `json:"size_bytes,omitempty"`
	FileType         string `json:"file_type,omitempty"`
	OutputType       string `json:"output_type,omitempty"` // 输出格式: pdf, jpg, png

	// 存储路径
	TempFilePath   string `json:"-"`
	OutputFilePath string `json:"-"`
	OutputURL      string `json:"url,omitempty"`

	// 多页输出支持
	PageCount      int    `json:"page_count,omitempty"`      // 总页数
	OutputURLs     string `json:"-"`                         // JSON格式存储多个URL

	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	CompletedAt *time.Time     `json:"completed_at,omitempty"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
}

// OutputFile 输出文件信息
type OutputFile struct {
	OriginalFilename  string `json:"original_filename"`
	ConvertedFilename string `json:"converted_filename"`
	URL               string `json:"url"`
	SizeBytes         int64  `json:"size_bytes"`
	PageNumber        int    `json:"page_number,omitempty"` // 页码，用于多页图片
	IsMainFile        bool   `json:"is_main_file"`          // 是否为主文件
}

// TaskResponse API响应结构
type TaskResponse struct {
	TaskID      string        `json:"task_id"`
	Status      TaskStatus    `json:"status"`
	Progress    int           `json:"progress,omitempty"`
	Message     string        `json:"message,omitempty"`
	ErrorCode   string        `json:"error_code,omitempty"`
	OutputFiles []OutputFile  `json:"output_files,omitempty"`
	CreatedAt   time.Time     `json:"created_at"`
	UpdatedAt   time.Time     `json:"updated_at,omitempty"`
	CompletedAt *time.Time    `json:"completed_at,omitempty"`
}

// ToResponse 转换为API响应格式
func (t *Task) ToResponse() TaskResponse {
	response := TaskResponse{
		TaskID:      t.TaskID,
		Status:      t.Status,
		Progress:    t.Progress,
		Message:     t.Message,
		ErrorCode:   t.ErrorCode,
		CreatedAt:   t.CreatedAt,
		UpdatedAt:   t.UpdatedAt,
		CompletedAt: t.CompletedAt,
	}
	
	// 如果任务完成，添加输出文件信息
	if t.Status == StatusCompleted && t.OutputURL != "" {
		// 检查是否有多页输出
		if t.OutputURLs != "" && t.PageCount > 1 {
			// 解析多页URL
			var urls []string
			if err := json.Unmarshal([]byte(t.OutputURLs), &urls); err == nil {
				response.OutputFiles = make([]OutputFile, len(urls))
				for i, url := range urls {
					response.OutputFiles[i] = OutputFile{
						OriginalFilename:  t.OriginalFilename,
						ConvertedFilename: fmt.Sprintf("%s_page_%d.%s",
							strings.TrimSuffix(t.ConvertedFilename, filepath.Ext(t.ConvertedFilename)),
							i+1,
							strings.TrimPrefix(filepath.Ext(t.ConvertedFilename), ".")),
						URL:        url,
						SizeBytes:  t.FileSize / int64(len(urls)), // 平均分配大小
						PageNumber: i + 1,
						IsMainFile: i == 0, // 第一页为主文件
					}
				}
			}
		} else {
			// 单页输出
			response.OutputFiles = []OutputFile{
				{
					OriginalFilename:  t.OriginalFilename,
					ConvertedFilename: t.ConvertedFilename,
					URL:               t.OutputURL,
					SizeBytes:         t.FileSize,
					PageNumber:        1,
					IsMainFile:        true,
				},
			}
		}
	}
	
	return response
}

// UpdateStatus 更新任务状态
func (t *Task) UpdateStatus(db *gorm.DB, status TaskStatus, message string) error {
	t.Status = status
	t.Message = message
	
	if status == StatusCompleted {
		now := time.Now()
		t.CompletedAt = &now
		t.Progress = 100
	}
	
	return db.Save(t).Error
}

// SetError 设置任务错误
func (t *Task) SetError(db *gorm.DB, errorCode, message string) error {
	t.Status = StatusFailed
	t.ErrorCode = errorCode
	t.Message = message
	return db.Save(t).Error
}
