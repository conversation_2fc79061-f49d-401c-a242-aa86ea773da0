package models

import (
	"time"

	"gorm.io/gorm"
)

// TaskStatus 任务状态枚举
type TaskStatus string

const (
	StatusQueued     TaskStatus = "queued"
	StatusProcessing TaskStatus = "processing"
	StatusCompleted  TaskStatus = "completed"
	StatusFailed     TaskStatus = "failed"
)

// Task 转换任务模型
type Task struct {
	ID          uint           `json:"-" gorm:"primarykey"`
	TaskID      string         `json:"task_id" gorm:"uniqueIndex;not null"`
	Status      TaskStatus     `json:"status" gorm:"not null;default:'queued'"`
	Progress    int            `json:"progress" gorm:"default:0"`
	Message     string         `json:"message,omitempty"`
	ErrorCode   string         `json:"error_code,omitempty"`

	// 文件信息
	OriginalFilename string `json:"original_filename,omitempty"`
	ConvertedFilename string `json:"converted_filename,omitempty"`
	FileSize         int64  `json:"size_bytes,omitempty"`
	FileType         string `json:"file_type,omitempty"`
	OutputType       string `json:"output_type,omitempty"` // 输出格式: pdf, jpg, png

	// 存储路径
	TempFilePath   string `json:"-"`
	OutputFilePath string `json:"-"`
	OutputURL      string `json:"url,omitempty"`

	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	CompletedAt *time.Time     `json:"completed_at,omitempty"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
}

// OutputFile 输出文件信息
type OutputFile struct {
	OriginalFilename  string `json:"original_filename"`
	ConvertedFilename string `json:"converted_filename"`
	URL               string `json:"url"`
	SizeBytes         int64  `json:"size_bytes"`
}

// TaskResponse API响应结构
type TaskResponse struct {
	TaskID      string        `json:"task_id"`
	Status      TaskStatus    `json:"status"`
	Progress    int           `json:"progress,omitempty"`
	Message     string        `json:"message,omitempty"`
	ErrorCode   string        `json:"error_code,omitempty"`
	OutputFiles []OutputFile  `json:"output_files,omitempty"`
	CreatedAt   time.Time     `json:"created_at"`
	UpdatedAt   time.Time     `json:"updated_at,omitempty"`
	CompletedAt *time.Time    `json:"completed_at,omitempty"`
}

// ToResponse 转换为API响应格式
func (t *Task) ToResponse() TaskResponse {
	response := TaskResponse{
		TaskID:      t.TaskID,
		Status:      t.Status,
		Progress:    t.Progress,
		Message:     t.Message,
		ErrorCode:   t.ErrorCode,
		CreatedAt:   t.CreatedAt,
		UpdatedAt:   t.UpdatedAt,
		CompletedAt: t.CompletedAt,
	}
	
	// 如果任务完成，添加输出文件信息
	if t.Status == StatusCompleted && t.OutputURL != "" {
		response.OutputFiles = []OutputFile{
			{
				OriginalFilename:  t.OriginalFilename,
				ConvertedFilename: t.ConvertedFilename,
				URL:               t.OutputURL,
				SizeBytes:         t.FileSize,
			},
		}
	}
	
	return response
}

// UpdateStatus 更新任务状态
func (t *Task) UpdateStatus(db *gorm.DB, status TaskStatus, message string) error {
	t.Status = status
	t.Message = message
	
	if status == StatusCompleted {
		now := time.Now()
		t.CompletedAt = &now
		t.Progress = 100
	}
	
	return db.Save(t).Error
}

// SetError 设置任务错误
func (t *Task) SetError(db *gorm.DB, errorCode, message string) error {
	t.Status = StatusFailed
	t.ErrorCode = errorCode
	t.Message = message
	return db.Save(t).Error
}
