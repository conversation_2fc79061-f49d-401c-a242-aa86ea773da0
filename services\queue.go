package services

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
	"github.com/sirupsen/logrus"

	"docx2pdf/config"
)

type QueueService struct {
	client *redis.Client
	ctx    context.Context
}

var Queue *QueueService

const (
	TaskQueueKey = "docx2pdf:tasks"
	TaskPrefix   = "docx2pdf:task:"
)

// TaskMessage 队列中的任务消息
type TaskMessage struct {
	TaskID           string `json:"task_id"`
	OriginalFilename string `json:"original_filename"`
	TempFilePath     string `json:"temp_file_path"`
	FileType         string `json:"file_type"`
	OutputType       string `json:"output_type"` // 输出格式: pdf, jpg, png
}

// InitQueue 初始化Redis队列服务
func InitQueue() error {
	rdb := redis.NewClient(&redis.Options{
		Addr:     config.AppConfig.RedisAddr,
		Password: config.AppConfig.RedisPassword,
		DB:       config.AppConfig.RedisDB,
	})
	
	ctx := context.Background()
	
	// 测试连接
	_, err := rdb.Ping(ctx).Result()
	if err != nil {
		return fmt.Errorf("failed to connect to Redis: %v", err)
	}
	
	Queue = &QueueService{
		client: rdb,
		ctx:    ctx,
	}
	
	logrus.Info("Redis queue service initialized successfully")
	return nil
}

// EnqueueTask 将任务加入队列
func (q *QueueService) EnqueueTask(taskMsg TaskMessage) error {
	data, err := json.Marshal(taskMsg)
	if err != nil {
		return fmt.Errorf("failed to marshal task message: %v", err)
	}
	
	err = q.client.LPush(q.ctx, TaskQueueKey, data).Err()
	if err != nil {
		return fmt.Errorf("failed to enqueue task: %v", err)
	}
	
	logrus.Infof("Task enqueued: %s", taskMsg.TaskID)
	return nil
}

// DequeueTask 从队列中取出任务
func (q *QueueService) DequeueTask(timeout time.Duration) (*TaskMessage, error) {
	result, err := q.client.BRPop(q.ctx, timeout, TaskQueueKey).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil // 超时，没有任务
		}
		return nil, fmt.Errorf("failed to dequeue task: %v", err)
	}
	
	if len(result) < 2 {
		return nil, fmt.Errorf("invalid queue result")
	}
	
	var taskMsg TaskMessage
	err = json.Unmarshal([]byte(result[1]), &taskMsg)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal task message: %v", err)
	}
	
	logrus.Infof("Task dequeued: %s", taskMsg.TaskID)
	return &taskMsg, nil
}

// SetTaskCache 设置任务缓存
func (q *QueueService) SetTaskCache(taskID string, data interface{}, expiration time.Duration) error {
	key := TaskPrefix + taskID
	
	jsonData, err := json.Marshal(data)
	if err != nil {
		return fmt.Errorf("failed to marshal task data: %v", err)
	}
	
	err = q.client.Set(q.ctx, key, jsonData, expiration).Err()
	if err != nil {
		return fmt.Errorf("failed to set task cache: %v", err)
	}
	
	return nil
}

// GetTaskCache 获取任务缓存
func (q *QueueService) GetTaskCache(taskID string, dest interface{}) error {
	key := TaskPrefix + taskID
	
	data, err := q.client.Get(q.ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return fmt.Errorf("task not found in cache")
		}
		return fmt.Errorf("failed to get task cache: %v", err)
	}
	
	err = json.Unmarshal([]byte(data), dest)
	if err != nil {
		return fmt.Errorf("failed to unmarshal task data: %v", err)
	}
	
	return nil
}

// DeleteTaskCache 删除任务缓存
func (q *QueueService) DeleteTaskCache(taskID string) error {
	key := TaskPrefix + taskID
	
	err := q.client.Del(q.ctx, key).Err()
	if err != nil {
		return fmt.Errorf("failed to delete task cache: %v", err)
	}
	
	return nil
}

// GetQueueLength 获取队列长度
func (q *QueueService) GetQueueLength() (int64, error) {
	length, err := q.client.LLen(q.ctx, TaskQueueKey).Result()
	if err != nil {
		return 0, fmt.Errorf("failed to get queue length: %v", err)
	}
	
	return length, nil
}
