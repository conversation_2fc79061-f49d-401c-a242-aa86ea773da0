package handlers

import (
	"io"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"

	"docx2pdf/models"
	"docx2pdf/services"
	"docx2pdf/utils"
)

type ConvertHandler struct {
	db *gorm.DB
}

func NewConvertHandler(db *gorm.DB) *ConvertHandler {
	return &ConvertHandler{db: db}
}

// ConvertFile 处理文件转换请求
func (h *ConvertHandler) ConvertFile(c *gin.Context) {
	// 获取上传的文件
	file, err := c.FormFile("files")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error_code": "MISSING_FILE",
			"message":    "No file uploaded",
		})
		return
	}
	
	// 验证文件
	if err := utils.ValidateFile(file); err != nil {
		if validationErr, ok := err.(*utils.FileValidationError); ok {
			c.<PERSON>(http.StatusBadRequest, gin.H{
				"error_code": validationErr.Code,
				"message":    validationErr.Message,
			})
		} else {
			c.JSON(http.StatusBadRequest, gin.H{
				"error_code": "VALIDATION_ERROR",
				"message":    err.Error(),
			})
		}
		return
	}
	
	// 生成任务ID
	taskID := uuid.New().String()
	
	// 读取文件内容
	fileContent, err := file.Open()
	if err != nil {
		logrus.Errorf("Failed to open uploaded file: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error_code": "FILE_READ_ERROR",
			"message":    "Failed to read uploaded file",
		})
		return
	}
	defer fileContent.Close()
	
	data, err := io.ReadAll(fileContent)
	if err != nil {
		logrus.Errorf("Failed to read file content: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error_code": "FILE_READ_ERROR",
			"message":    "Failed to read file content",
		})
		return
	}
	
	// 保存临时文件
	tempFilePath, err := services.SaveTempFile(taskID, file.Filename, data)
	if err != nil {
		logrus.Errorf("Failed to save temp file: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error_code": "TEMP_FILE_ERROR",
			"message":    "Failed to save temporary file",
		})
		return
	}
	
	// 创建任务记录
	task := &models.Task{
		TaskID:           taskID,
		Status:           models.StatusQueued,
		OriginalFilename: file.Filename,
		FileSize:         file.Size,
		FileType:         utils.GetFileType(file.Filename),
		TempFilePath:     tempFilePath,
	}
	
	if err := h.db.Create(task).Error; err != nil {
		logrus.Errorf("Failed to create task record: %v", err)
		// 清理临时文件
		services.CleanupTempFile(tempFilePath)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error_code": "DATABASE_ERROR",
			"message":    "Failed to create conversion task",
		})
		return
	}
	
	// 将任务加入队列
	taskMsg := services.TaskMessage{
		TaskID:           taskID,
		OriginalFilename: file.Filename,
		TempFilePath:     tempFilePath,
		FileType:         task.FileType,
	}
	
	if err := services.Queue.EnqueueTask(taskMsg); err != nil {
		logrus.Errorf("Failed to enqueue task: %v", err)
		// 更新任务状态为失败
		task.SetError(h.db, "QUEUE_ERROR", "Failed to queue conversion task")
		// 清理临时文件
		services.CleanupTempFile(tempFilePath)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error_code": "QUEUE_ERROR",
			"message":    "Failed to queue conversion task",
		})
		return
	}
	
	logrus.Infof("Conversion task created successfully: %s", taskID)
	
	// 返回任务ID
	c.JSON(http.StatusAccepted, gin.H{
		"task_id": taskID,
		"status":  "queued",
		"message": "Conversion task created successfully. Please poll /api/v1/tasks/" + taskID + " for status.",
	})
}
