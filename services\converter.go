package services

import (
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"strings"
	"time"

	"github.com/sirupsen/logrus"

	"docx2pdf/utils"
)

type ConverterService struct{}

var Converter *ConverterService

// InitConverter 初始化转换服务
func InitConverter() {
	Converter = &ConverterService{}
	logrus.Infof("Converter service initialized for %s/%s", runtime.GOOS, runtime.GOARCH)
}

// ConvertFile 根据输出类型转换文件
func (c *ConverterService) ConvertFile(inputPath, outputPath, outputType string) error {
	// 确保输出目录存在
	outputDir := filepath.Dir(outputPath)
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		return fmt.Errorf("failed to create output directory: %v", err)
	}

	switch outputType {
	case "pdf":
		return c.ConvertToPDF(inputPath, outputPath)
	case "jpg", "png":
		return c.ConvertToImage(inputPath, outputPath, outputType)
	default:
		return fmt.Errorf("unsupported output type: %s", outputType)
	}
}

// ConvertToPDF 将文件转换为PDF
func (c *ConverterService) ConvertToPDF(inputPath, outputPath string) error {
	// 确保输出目录存在
	outputDir := filepath.Dir(outputPath)
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		return fmt.Errorf("failed to create output directory: %v", err)
	}

	// 根据操作系统选择转换方法
	switch runtime.GOOS {
	case "windows":
		return c.convertWithLibreOfficeWindows(inputPath, outputPath)
	case "linux", "darwin":
		return c.convertWithLibreOfficeUnix(inputPath, outputPath)
	default:
		return fmt.Errorf("unsupported operating system: %s", runtime.GOOS)
	}
}

// convertWithLibreOfficeWindows 在Windows上使用LibreOffice转换
func (c *ConverterService) convertWithLibreOfficeWindows(inputPath, outputPath string) error {
	// 常见的LibreOffice安装路径
	possiblePaths := []string{
		"C:\\Program Files\\LibreOffice\\program\\soffice.exe",
		"C:\\Program Files (x86)\\LibreOffice\\program\\soffice.exe",
		"soffice.exe", // 如果在PATH中
	}

	var sofficeCmd string
	for _, path := range possiblePaths {
		if _, err := os.Stat(path); err == nil {
			sofficeCmd = path
			break
		}
		// 检查PATH中是否存在
		if _, err := exec.LookPath(path); err == nil {
			sofficeCmd = path
			break
		}
	}

	if sofficeCmd == "" {
		return fmt.Errorf("LibreOffice not found. Please install LibreOffice")
	}

	return c.executeLibreOfficeConversion(sofficeCmd, inputPath, outputPath)
}

// convertWithLibreOfficeUnix 在Unix系统上使用LibreOffice转换
func (c *ConverterService) convertWithLibreOfficeUnix(inputPath, outputPath string) error {
	// 按优先级检查LibreOffice命令
	possibleCommands := []string{
		"libreoffice",
		"soffice",
		"/usr/bin/libreoffice",
		"/usr/bin/soffice",
		"/opt/libreoffice/program/soffice",
		"/snap/bin/libreoffice",
	}

	var sofficeCmd string
	for _, cmd := range possibleCommands {
		if _, err := exec.LookPath(cmd); err == nil {
			sofficeCmd = cmd
			break
		}
		// 直接检查文件是否存在
		if _, err := os.Stat(cmd); err == nil {
			sofficeCmd = cmd
			break
		}
	}

	if sofficeCmd == "" {
		return fmt.Errorf("LibreOffice not found. Please install LibreOffice using: apt-get install libreoffice")
	}

	logrus.Infof("Using LibreOffice command: %s", sofficeCmd)
	return c.executeLibreOfficeConversion(sofficeCmd, inputPath, outputPath)
}

// executeLibreOfficeConversion 执行LibreOffice转换
func (c *ConverterService) executeLibreOfficeConversion(sofficeCmd, inputPath, outputPath string) error {
	outputDir := filepath.Dir(outputPath)

	// LibreOffice命令参数
	args := []string{
		"--headless",
		"--invisible",
		"--nodefault",
		"--nolockcheck",
		"--nologo",
		"--norestore",
		"--convert-to", "pdf",
		"--outdir", outputDir,
		inputPath,
	}

	logrus.Infof("Executing LibreOffice conversion: %s %s", sofficeCmd, strings.Join(args, " "))

	// 创建带超时的命令
	cmd := exec.Command(sofficeCmd, args...)

	// 设置环境变量，避免GUI相关问题
	cmd.Env = append(os.Environ(),
		"DISPLAY=",
		"HOME=/tmp",
		"TMPDIR=/tmp",
	)

	// 执行命令并设置超时
	done := make(chan error, 1)
	go func() {
		output, err := cmd.CombinedOutput()
		if err != nil {
			logrus.Errorf("LibreOffice conversion failed: %v, output: %s", err, string(output))
			done <- fmt.Errorf("conversion failed: %v, output: %s", err, string(output))
		} else {
			logrus.Infof("LibreOffice conversion output: %s", string(output))
			done <- nil
		}
	}()

	// 等待完成或超时
	select {
	case err := <-done:
		if err != nil {
			return err
		}
	case <-time.After(5 * time.Minute): // 5分钟超时
		if cmd.Process != nil {
			cmd.Process.Kill()
		}
		return fmt.Errorf("conversion timeout after 5 minutes")
	}

	// LibreOffice会生成与输入文件同名但扩展名为.pdf的文件
	inputFilename := filepath.Base(inputPath)
	expectedOutputFilename := utils.GenerateOutputFilename(inputFilename)
	expectedOutputPath := filepath.Join(outputDir, expectedOutputFilename)

	// 等待文件生成（最多等待30秒）
	for i := 0; i < 30; i++ {
		if _, err := os.Stat(expectedOutputPath); err == nil {
			break
		}
		time.Sleep(1 * time.Second)
	}

	// 检查生成的文件是否存在
	if _, err := os.Stat(expectedOutputPath); os.IsNotExist(err) {
		return fmt.Errorf("conversion completed but output file not found: %s", expectedOutputPath)
	}

	// 如果输出路径不同，重命名文件
	if expectedOutputPath != outputPath {
		if err := os.Rename(expectedOutputPath, outputPath); err != nil {
			return fmt.Errorf("failed to rename output file: %v", err)
		}
	}

	logrus.Infof("Conversion completed successfully: %s -> %s", inputPath, outputPath)
	return nil
}

// ValidateConversion 验证转换结果
func (c *ConverterService) ValidateConversion(outputPath, outputType string) error {
	// 检查文件是否存在
	info, err := os.Stat(outputPath)
	if err != nil {
		return fmt.Errorf("output file does not exist: %v", err)
	}

	// 检查文件大小
	if info.Size() == 0 {
		return fmt.Errorf("output file is empty")
	}

	// 根据输出类型进行不同的验证
	switch outputType {
	case "pdf":
		return c.validatePDF(outputPath)
	case "jpg", "png":
		return c.validateImage(outputPath, outputType)
	default:
		return fmt.Errorf("unsupported output type for validation: %s", outputType)
	}
}

// validatePDF 验证PDF文件
func (c *ConverterService) validatePDF(outputPath string) error {
	file, err := os.Open(outputPath)
	if err != nil {
		return fmt.Errorf("failed to open output file: %v", err)
	}
	defer file.Close()

	header := make([]byte, 4)
	_, err = file.Read(header)
	if err != nil {
		return fmt.Errorf("failed to read file header: %v", err)
	}

	if string(header) != "%PDF" {
		return fmt.Errorf("output file is not a valid PDF")
	}

	logrus.Infof("PDF validation passed: %s", outputPath)
	return nil
}

// validateImage 验证图片文件
func (c *ConverterService) validateImage(outputPath, imageFormat string) error {
	file, err := os.Open(outputPath)
	if err != nil {
		return fmt.Errorf("failed to open output file: %v", err)
	}
	defer file.Close()

	// 读取文件头进行简单验证
	header := make([]byte, 8)
	_, err = file.Read(header)
	if err != nil {
		return fmt.Errorf("failed to read file header: %v", err)
	}

	switch imageFormat {
	case "jpg":
		// JPEG文件头: FF D8 FF
		if header[0] != 0xFF || header[1] != 0xD8 || header[2] != 0xFF {
			return fmt.Errorf("output file is not a valid JPEG")
		}
	case "png":
		// PNG文件头: 89 50 4E 47 0D 0A 1A 0A
		pngHeader := []byte{0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A}
		for i, b := range pngHeader {
			if header[i] != b {
				return fmt.Errorf("output file is not a valid PNG")
			}
		}
	}

	logrus.Infof("Image validation passed: %s", outputPath)
	return nil
}

// ConvertToImage 将文件转换为图片
func (c *ConverterService) ConvertToImage(inputPath, outputPath, imageFormat string) error {
	// 首先转换为PDF
	tempPDFPath := strings.TrimSuffix(outputPath, filepath.Ext(outputPath)) + "_temp.pdf"
	defer os.Remove(tempPDFPath) // 清理临时PDF文件

	// 转换为PDF
	if err := c.ConvertToPDF(inputPath, tempPDFPath); err != nil {
		return fmt.Errorf("failed to convert to PDF: %v", err)
	}

	// 将PDF的所有页面转换为图片
	return c.convertPDFToImages(tempPDFPath, outputPath, imageFormat)
}

// convertPDFToImages 将PDF的所有页面转换为图片
func (c *ConverterService) convertPDFToImages(pdfPath, outputPath, imageFormat string) error {
	// 检查是否有ImageMagick或pdftoppm可用
	var convertCmd string
	var useImageMagick bool

	// 优先尝试ImageMagick
	if _, err := exec.LookPath("magick"); err == nil {
		convertCmd = "magick"
		useImageMagick = true
	} else if _, err := exec.LookPath("convert"); err == nil {
		convertCmd = "convert"
		useImageMagick = true
	} else if _, err := exec.LookPath("pdftoppm"); err == nil {
		convertCmd = "pdftoppm"
		useImageMagick = false
	} else {
		return fmt.Errorf("neither ImageMagick nor pdftoppm found. Please install ImageMagick or poppler-utils")
	}

	outputDir := filepath.Dir(outputPath)
	outputBaseName := strings.TrimSuffix(filepath.Base(outputPath), filepath.Ext(outputPath))

	if useImageMagick {
		return c.convertPDFWithImageMagick(convertCmd, pdfPath, outputDir, outputBaseName, imageFormat)
	} else {
		return c.convertPDFWithPdftoppm(pdfPath, outputDir, outputBaseName, imageFormat)
	}
}

// convertPDFWithImageMagick 使用ImageMagick转换PDF为图片
func (c *ConverterService) convertPDFWithImageMagick(convertCmd, pdfPath, outputDir, outputBaseName, imageFormat string) error {
	// ImageMagick命令参数
	outputPattern := filepath.Join(outputDir, fmt.Sprintf("%s_page_%%d.%s", outputBaseName, imageFormat))

	args := []string{
		"-density", "300",        // 设置DPI为300
		"-quality", "90",         // 设置质量为90%
		pdfPath,                  // 输入PDF文件
		outputPattern,            // 输出文件模式
	}

	// 如果是PNG格式，添加透明背景支持
	if imageFormat == "png" {
		args = append([]string{"-background", "white", "-alpha", "remove"}, args...)
	}

	logrus.Infof("Executing ImageMagick conversion: %s %s", convertCmd, strings.Join(args, " "))

	// 创建带超时的命令
	cmd := exec.Command(convertCmd, args...)

	// 执行命令并设置超时
	done := make(chan error, 1)
	go func() {
		output, err := cmd.CombinedOutput()
		if err != nil {
			logrus.Errorf("ImageMagick conversion failed: %v, output: %s", err, string(output))
			done <- fmt.Errorf("image conversion failed: %v, output: %s", err, string(output))
		} else {
			logrus.Infof("ImageMagick conversion output: %s", string(output))
			done <- nil
		}
	}()

	// 等待完成或超时
	select {
	case err := <-done:
		if err != nil {
			return err
		}
	case <-time.After(3 * time.Minute): // 3分钟超时
		if cmd.Process != nil {
			cmd.Process.Kill()
		}
		return fmt.Errorf("image conversion timeout after 3 minutes")
	}

	// 检查生成的文件
	pattern := filepath.Join(outputDir, fmt.Sprintf("%s_page_*.%s", outputBaseName, imageFormat))
	files, err := filepath.Glob(pattern)
	if err != nil {
		return fmt.Errorf("failed to search for generated images: %v", err)
	}

	if len(files) == 0 {
		return fmt.Errorf("no image files generated")
	}

	logrus.Infof("ImageMagick conversion completed: %d pages generated", len(files))
	return nil
}

// convertPDFWithPdftoppm 使用pdftoppm转换PDF为图片
func (c *ConverterService) convertPDFWithPdftoppm(pdfPath, outputDir, outputBaseName, imageFormat string) error {
	// pdftoppm命令参数
	outputPrefix := filepath.Join(outputDir, outputBaseName+"_page")

	var args []string
	switch imageFormat {
	case "jpg":
		args = []string{
			"-jpeg",
			"-r", "300",              // 设置DPI为300
			pdfPath,                  // 输入PDF文件
			outputPrefix,             // 输出文件前缀
		}
	case "png":
		args = []string{
			"-png",
			"-r", "300",              // 设置DPI为300
			pdfPath,                  // 输入PDF文件
			outputPrefix,             // 输出文件前缀
		}
	default:
		return fmt.Errorf("unsupported image format for pdftoppm: %s", imageFormat)
	}

	logrus.Infof("Executing pdftoppm conversion: pdftoppm %s", strings.Join(args, " "))

	// 创建带超时的命令
	cmd := exec.Command("pdftoppm", args...)

	// 执行命令并设置超时
	done := make(chan error, 1)
	go func() {
		output, err := cmd.CombinedOutput()
		if err != nil {
			logrus.Errorf("pdftoppm conversion failed: %v, output: %s", err, string(output))
			done <- fmt.Errorf("image conversion failed: %v, output: %s", err, string(output))
		} else {
			logrus.Infof("pdftoppm conversion output: %s", string(output))
			done <- nil
		}
	}()

	// 等待完成或超时
	select {
	case err := <-done:
		if err != nil {
			return err
		}
	case <-time.After(3 * time.Minute): // 3分钟超时
		if cmd.Process != nil {
			cmd.Process.Kill()
		}
		return fmt.Errorf("image conversion timeout after 3 minutes")
	}

	// 检查生成的文件
	pattern := filepath.Join(outputDir, fmt.Sprintf("%s_page-*.%s", outputBaseName, imageFormat))
	files, err := filepath.Glob(pattern)
	if err != nil {
		return fmt.Errorf("failed to search for generated images: %v", err)
	}

	if len(files) == 0 {
		return fmt.Errorf("no image files generated")
	}

	// 重命名文件以符合我们的命名规范
	for i, file := range files {
		newName := filepath.Join(outputDir, fmt.Sprintf("%s_page_%d.%s", outputBaseName, i+1, imageFormat))
		if err := os.Rename(file, newName); err != nil {
			logrus.Warnf("Failed to rename file %s to %s: %v", file, newName, err)
		}
	}

	logrus.Infof("pdftoppm conversion completed: %d pages generated", len(files))
	return nil
}

// convertWithLibreOfficeToImage 使用LibreOffice直接转换为图片
func (c *ConverterService) convertWithLibreOfficeToImage(inputPath, outputPath, imageFormat string) error {
	// 确保输出目录存在
	outputDir := filepath.Dir(outputPath)
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		return fmt.Errorf("failed to create output directory: %v", err)
	}

	// 根据操作系统选择LibreOffice命令
	var sofficeCmd string
	switch runtime.GOOS {
	case "windows":
		sofficeCmd = c.findLibreOfficeWindows()
	case "linux", "darwin":
		sofficeCmd = c.findLibreOfficeUnix()
	default:
		return fmt.Errorf("unsupported operating system: %s", runtime.GOOS)
	}

	if sofficeCmd == "" {
		return fmt.Errorf("LibreOffice not found. Please install LibreOffice")
	}

	// 创建临时输出目录用于存放所有页面的图片
	tempOutputDir := filepath.Join(outputDir, "temp_images_"+filepath.Base(inputPath))
	if err := os.MkdirAll(tempOutputDir, 0755); err != nil {
		return fmt.Errorf("failed to create temp output directory: %v", err)
	}
	defer os.RemoveAll(tempOutputDir) // 清理临时目录

	// LibreOffice命令参数 - 转换为图片格式
	args := []string{
		"--headless",
		"--invisible",
		"--nodefault",
		"--nolockcheck",
		"--nologo",
		"--norestore",
		"--convert-to", imageFormat,
		"--outdir", tempOutputDir,
		inputPath,
	}

	logrus.Infof("Executing LibreOffice image conversion: %s %s", sofficeCmd, strings.Join(args, " "))

	// 创建带超时的命令
	cmd := exec.Command(sofficeCmd, args...)

	// 设置环境变量，避免GUI相关问题
	cmd.Env = append(os.Environ(),
		"DISPLAY=",
		"HOME=/tmp",
		"TMPDIR=/tmp",
	)

	// 执行命令并设置超时
	done := make(chan error, 1)
	go func() {
		output, err := cmd.CombinedOutput()
		if err != nil {
			logrus.Errorf("LibreOffice image conversion failed: %v, output: %s", err, string(output))
			done <- fmt.Errorf("image conversion failed: %v, output: %s", err, string(output))
		} else {
			logrus.Infof("LibreOffice image conversion output: %s", string(output))
			done <- nil
		}
	}()

	// 等待完成或超时
	select {
	case err := <-done:
		if err != nil {
			return err
		}
	case <-time.After(5 * time.Minute): // 5分钟超时
		if cmd.Process != nil {
			cmd.Process.Kill()
		}
		return fmt.Errorf("image conversion timeout after 5 minutes")
	}

	// 等待文件生成（最多等待30秒）
	time.Sleep(2 * time.Second) // 给LibreOffice一些时间完成文件写入

	// 查找生成的图片文件
	files, err := filepath.Glob(filepath.Join(tempOutputDir, "*."+imageFormat))
	if err != nil {
		return fmt.Errorf("failed to search for generated images: %v", err)
	}

	if len(files) == 0 {
		return fmt.Errorf("no image files generated in: %s", tempOutputDir)
	}

	// 如果只有一个文件，直接移动到目标位置
	if len(files) == 1 {
		if err := os.Rename(files[0], outputPath); err != nil {
			return fmt.Errorf("failed to move single image file: %v", err)
		}
		logrus.Infof("Single page image conversion completed: %s", outputPath)
		return nil
	}

	// 多个文件的情况，创建一个包含所有页面的压缩包或者合并图片
	return c.handleMultipleImageFiles(files, outputPath, imageFormat)
}

// findLibreOfficeWindows 在Windows上查找LibreOffice
func (c *ConverterService) findLibreOfficeWindows() string {
	possiblePaths := []string{
		"C:\\Program Files\\LibreOffice\\program\\soffice.exe",
		"C:\\Program Files (x86)\\LibreOffice\\program\\soffice.exe",
		"soffice.exe", // 如果在PATH中
	}

	for _, path := range possiblePaths {
		if _, err := os.Stat(path); err == nil {
			return path
		}
		// 检查PATH中是否存在
		if _, err := exec.LookPath(path); err == nil {
			return path
		}
	}
	return ""
}

// findLibreOfficeUnix 在Unix系统上查找LibreOffice
func (c *ConverterService) findLibreOfficeUnix() string {
	possibleCommands := []string{
		"libreoffice",
		"soffice",
		"/usr/bin/libreoffice",
		"/usr/bin/soffice",
		"/opt/libreoffice/program/soffice",
		"/snap/bin/libreoffice",
	}

	for _, cmd := range possibleCommands {
		if _, err := exec.LookPath(cmd); err == nil {
			return cmd
		}
		// 直接检查文件是否存在
		if _, err := os.Stat(cmd); err == nil {
			return cmd
		}
	}
	return ""
}

// handleMultipleImageFiles 处理多个图片文件
func (c *ConverterService) handleMultipleImageFiles(files []string, outputPath, imageFormat string) error {
	// 对文件进行排序，确保页面顺序正确
	// LibreOffice通常会生成类似 "filename-1.jpg", "filename-2.jpg" 的文件
	// 我们需要按数字顺序排序

	// 简单的排序方法：按文件名排序
	for i := 0; i < len(files)-1; i++ {
		for j := i + 1; j < len(files); j++ {
			if filepath.Base(files[i]) > filepath.Base(files[j]) {
				files[i], files[j] = files[j], files[i]
			}
		}
	}

	// 获取输出文件的基本信息
	outputDir := filepath.Dir(outputPath)
	outputBaseName := strings.TrimSuffix(filepath.Base(outputPath), filepath.Ext(outputPath))

	// 为每个页面创建单独的文件
	for i, file := range files {
		pageOutputPath := filepath.Join(outputDir, fmt.Sprintf("%s_page_%d.%s", outputBaseName, i+1, imageFormat))
		if err := os.Rename(file, pageOutputPath); err != nil {
			return fmt.Errorf("failed to move page %d image file: %v", i+1, err)
		}
		logrus.Infof("Created page %d image: %s", i+1, pageOutputPath)
	}

	// 将第一页作为主输出文件
	firstPagePath := filepath.Join(outputDir, fmt.Sprintf("%s_page_1.%s", outputBaseName, imageFormat))
	if err := copyFile(firstPagePath, outputPath); err != nil {
		return fmt.Errorf("failed to copy first page as main output: %v", err)
	}

	logrus.Infof("Multi-page image conversion completed: %d pages generated", len(files))
	return nil
}

// copyFile 复制文件
func copyFile(src, dst string) error {
	sourceFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer sourceFile.Close()

	destFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer destFile.Close()

	_, err = destFile.ReadFrom(sourceFile)
	return err
}
