package services

import (
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"strings"
	"time"

	"github.com/sirupsen/logrus"

	"docx2pdf/utils"
)

type ConverterService struct{}

var Converter *ConverterService

// InitConverter 初始化转换服务
func InitConverter() {
	Converter = &ConverterService{}
	logrus.Infof("Converter service initialized for %s/%s", runtime.GOOS, runtime.GOARCH)
}

// ConvertToPDF 将文件转换为PDF
func (c *ConverterService) ConvertToPDF(inputPath, outputPath string) error {
	// 确保输出目录存在
	outputDir := filepath.Dir(outputPath)
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		return fmt.Errorf("failed to create output directory: %v", err)
	}

	// 根据操作系统选择转换方法
	switch runtime.GOOS {
	case "windows":
		return c.convertWithLibreOfficeWindows(inputPath, outputPath)
	case "linux", "darwin":
		return c.convertWithLibreOfficeUnix(inputPath, outputPath)
	default:
		return fmt.Errorf("unsupported operating system: %s", runtime.GOOS)
	}
}

// convertWithLibreOfficeWindows 在Windows上使用LibreOffice转换
func (c *ConverterService) convertWithLibreOfficeWindows(inputPath, outputPath string) error {
	// 常见的LibreOffice安装路径
	possiblePaths := []string{
		"C:\\Program Files\\LibreOffice\\program\\soffice.exe",
		"C:\\Program Files (x86)\\LibreOffice\\program\\soffice.exe",
		"soffice.exe", // 如果在PATH中
	}

	var sofficeCmd string
	for _, path := range possiblePaths {
		if _, err := os.Stat(path); err == nil {
			sofficeCmd = path
			break
		}
		// 检查PATH中是否存在
		if _, err := exec.LookPath(path); err == nil {
			sofficeCmd = path
			break
		}
	}

	if sofficeCmd == "" {
		return fmt.Errorf("LibreOffice not found. Please install LibreOffice")
	}

	return c.executeLibreOfficeConversion(sofficeCmd, inputPath, outputPath)
}

// convertWithLibreOfficeUnix 在Unix系统上使用LibreOffice转换
func (c *ConverterService) convertWithLibreOfficeUnix(inputPath, outputPath string) error {
	// 按优先级检查LibreOffice命令
	possibleCommands := []string{
		"libreoffice",
		"soffice",
		"/usr/bin/libreoffice",
		"/usr/bin/soffice",
		"/opt/libreoffice/program/soffice",
		"/snap/bin/libreoffice",
	}

	var sofficeCmd string
	for _, cmd := range possibleCommands {
		if _, err := exec.LookPath(cmd); err == nil {
			sofficeCmd = cmd
			break
		}
		// 直接检查文件是否存在
		if _, err := os.Stat(cmd); err == nil {
			sofficeCmd = cmd
			break
		}
	}

	if sofficeCmd == "" {
		return fmt.Errorf("LibreOffice not found. Please install LibreOffice using: apt-get install libreoffice")
	}

	logrus.Infof("Using LibreOffice command: %s", sofficeCmd)
	return c.executeLibreOfficeConversion(sofficeCmd, inputPath, outputPath)
}

// executeLibreOfficeConversion 执行LibreOffice转换
func (c *ConverterService) executeLibreOfficeConversion(sofficeCmd, inputPath, outputPath string) error {
	outputDir := filepath.Dir(outputPath)

	// LibreOffice命令参数
	args := []string{
		"--headless",
		"--invisible",
		"--nodefault",
		"--nolockcheck",
		"--nologo",
		"--norestore",
		"--convert-to", "pdf",
		"--outdir", outputDir,
		inputPath,
	}

	logrus.Infof("Executing LibreOffice conversion: %s %s", sofficeCmd, strings.Join(args, " "))

	// 创建带超时的命令
	cmd := exec.Command(sofficeCmd, args...)

	// 设置环境变量，避免GUI相关问题
	cmd.Env = append(os.Environ(),
		"DISPLAY=",
		"HOME=/tmp",
		"TMPDIR=/tmp",
	)

	// 执行命令并设置超时
	done := make(chan error, 1)
	go func() {
		output, err := cmd.CombinedOutput()
		if err != nil {
			logrus.Errorf("LibreOffice conversion failed: %v, output: %s", err, string(output))
			done <- fmt.Errorf("conversion failed: %v, output: %s", err, string(output))
		} else {
			logrus.Infof("LibreOffice conversion output: %s", string(output))
			done <- nil
		}
	}()

	// 等待完成或超时
	select {
	case err := <-done:
		if err != nil {
			return err
		}
	case <-time.After(5 * time.Minute): // 5分钟超时
		if cmd.Process != nil {
			cmd.Process.Kill()
		}
		return fmt.Errorf("conversion timeout after 5 minutes")
	}

	// LibreOffice会生成与输入文件同名但扩展名为.pdf的文件
	inputFilename := filepath.Base(inputPath)
	expectedOutputFilename := utils.GenerateOutputFilename(inputFilename)
	expectedOutputPath := filepath.Join(outputDir, expectedOutputFilename)

	// 等待文件生成（最多等待30秒）
	for i := 0; i < 30; i++ {
		if _, err := os.Stat(expectedOutputPath); err == nil {
			break
		}
		time.Sleep(1 * time.Second)
	}

	// 检查生成的文件是否存在
	if _, err := os.Stat(expectedOutputPath); os.IsNotExist(err) {
		return fmt.Errorf("conversion completed but output file not found: %s", expectedOutputPath)
	}

	// 如果输出路径不同，重命名文件
	if expectedOutputPath != outputPath {
		if err := os.Rename(expectedOutputPath, outputPath); err != nil {
			return fmt.Errorf("failed to rename output file: %v", err)
		}
	}

	logrus.Infof("Conversion completed successfully: %s -> %s", inputPath, outputPath)
	return nil
}

// ValidateConversion 验证转换结果
func (c *ConverterService) ValidateConversion(outputPath string) error {
	// 检查文件是否存在
	info, err := os.Stat(outputPath)
	if err != nil {
		return fmt.Errorf("output file does not exist: %v", err)
	}

	// 检查文件大小
	if info.Size() == 0 {
		return fmt.Errorf("output file is empty")
	}

	// 简单的PDF文件头检查
	file, err := os.Open(outputPath)
	if err != nil {
		return fmt.Errorf("failed to open output file: %v", err)
	}
	defer file.Close()

	header := make([]byte, 4)
	_, err = file.Read(header)
	if err != nil {
		return fmt.Errorf("failed to read file header: %v", err)
	}

	if string(header) != "%PDF" {
		return fmt.Errorf("output file is not a valid PDF")
	}

	logrus.Infof("Conversion validation passed: %s", outputPath)
	return nil
}
