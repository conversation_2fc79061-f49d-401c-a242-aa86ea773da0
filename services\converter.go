package services

import (
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"strings"
	"time"

	"github.com/sirupsen/logrus"

	"docx2pdf/utils"
)

type ConverterService struct{}

var Converter *ConverterService

// InitConverter 初始化转换服务
func InitConverter() {
	Converter = &ConverterService{}
	logrus.Infof("Converter service initialized for %s/%s", runtime.GOOS, runtime.GOARCH)
}

// ConvertFile 根据输出类型转换文件
func (c *ConverterService) ConvertFile(inputPath, outputPath, outputType string) error {
	// 确保输出目录存在
	outputDir := filepath.Dir(outputPath)
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		return fmt.Errorf("failed to create output directory: %v", err)
	}

	switch outputType {
	case "pdf":
		return c.ConvertToPDF(inputPath, outputPath)
	case "jpg", "png":
		return c.ConvertToImage(inputPath, outputPath, outputType)
	default:
		return fmt.Errorf("unsupported output type: %s", outputType)
	}
}

// ConvertToPDF 将文件转换为PDF
func (c *ConverterService) ConvertToPDF(inputPath, outputPath string) error {
	// 确保输出目录存在
	outputDir := filepath.Dir(outputPath)
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		return fmt.Errorf("failed to create output directory: %v", err)
	}

	// 根据操作系统选择转换方法
	switch runtime.GOOS {
	case "windows":
		return c.convertWithLibreOfficeWindows(inputPath, outputPath)
	case "linux", "darwin":
		return c.convertWithLibreOfficeUnix(inputPath, outputPath)
	default:
		return fmt.Errorf("unsupported operating system: %s", runtime.GOOS)
	}
}

// convertWithLibreOfficeWindows 在Windows上使用LibreOffice转换
func (c *ConverterService) convertWithLibreOfficeWindows(inputPath, outputPath string) error {
	// 常见的LibreOffice安装路径
	possiblePaths := []string{
		"C:\\Program Files\\LibreOffice\\program\\soffice.exe",
		"C:\\Program Files (x86)\\LibreOffice\\program\\soffice.exe",
		"soffice.exe", // 如果在PATH中
	}

	var sofficeCmd string
	for _, path := range possiblePaths {
		if _, err := os.Stat(path); err == nil {
			sofficeCmd = path
			break
		}
		// 检查PATH中是否存在
		if _, err := exec.LookPath(path); err == nil {
			sofficeCmd = path
			break
		}
	}

	if sofficeCmd == "" {
		return fmt.Errorf("LibreOffice not found. Please install LibreOffice")
	}

	return c.executeLibreOfficeConversion(sofficeCmd, inputPath, outputPath)
}

// convertWithLibreOfficeUnix 在Unix系统上使用LibreOffice转换
func (c *ConverterService) convertWithLibreOfficeUnix(inputPath, outputPath string) error {
	// 按优先级检查LibreOffice命令
	possibleCommands := []string{
		"libreoffice",
		"soffice",
		"/usr/bin/libreoffice",
		"/usr/bin/soffice",
		"/opt/libreoffice/program/soffice",
		"/snap/bin/libreoffice",
	}

	var sofficeCmd string
	for _, cmd := range possibleCommands {
		if _, err := exec.LookPath(cmd); err == nil {
			sofficeCmd = cmd
			break
		}
		// 直接检查文件是否存在
		if _, err := os.Stat(cmd); err == nil {
			sofficeCmd = cmd
			break
		}
	}

	if sofficeCmd == "" {
		return fmt.Errorf("LibreOffice not found. Please install LibreOffice using: apt-get install libreoffice")
	}

	logrus.Infof("Using LibreOffice command: %s", sofficeCmd)
	return c.executeLibreOfficeConversion(sofficeCmd, inputPath, outputPath)
}

// executeLibreOfficeConversion 执行LibreOffice转换
func (c *ConverterService) executeLibreOfficeConversion(sofficeCmd, inputPath, outputPath string) error {
	outputDir := filepath.Dir(outputPath)

	// LibreOffice命令参数
	args := []string{
		"--headless",
		"--invisible",
		"--nodefault",
		"--nolockcheck",
		"--nologo",
		"--norestore",
		"--convert-to", "pdf",
		"--outdir", outputDir,
		inputPath,
	}

	logrus.Infof("Executing LibreOffice conversion: %s %s", sofficeCmd, strings.Join(args, " "))

	// 创建带超时的命令
	cmd := exec.Command(sofficeCmd, args...)

	// 设置环境变量，避免GUI相关问题
	cmd.Env = append(os.Environ(),
		"DISPLAY=",
		"HOME=/tmp",
		"TMPDIR=/tmp",
	)

	// 执行命令并设置超时
	done := make(chan error, 1)
	go func() {
		output, err := cmd.CombinedOutput()
		if err != nil {
			logrus.Errorf("LibreOffice conversion failed: %v, output: %s", err, string(output))
			done <- fmt.Errorf("conversion failed: %v, output: %s", err, string(output))
		} else {
			logrus.Infof("LibreOffice conversion output: %s", string(output))
			done <- nil
		}
	}()

	// 等待完成或超时
	select {
	case err := <-done:
		if err != nil {
			return err
		}
	case <-time.After(5 * time.Minute): // 5分钟超时
		if cmd.Process != nil {
			cmd.Process.Kill()
		}
		return fmt.Errorf("conversion timeout after 5 minutes")
	}

	// LibreOffice会生成与输入文件同名但扩展名为.pdf的文件
	inputFilename := filepath.Base(inputPath)
	expectedOutputFilename := utils.GenerateOutputFilename(inputFilename)
	expectedOutputPath := filepath.Join(outputDir, expectedOutputFilename)

	// 等待文件生成（最多等待30秒）
	for i := 0; i < 30; i++ {
		if _, err := os.Stat(expectedOutputPath); err == nil {
			break
		}
		time.Sleep(1 * time.Second)
	}

	// 检查生成的文件是否存在
	if _, err := os.Stat(expectedOutputPath); os.IsNotExist(err) {
		return fmt.Errorf("conversion completed but output file not found: %s", expectedOutputPath)
	}

	// 如果输出路径不同，重命名文件
	if expectedOutputPath != outputPath {
		if err := os.Rename(expectedOutputPath, outputPath); err != nil {
			return fmt.Errorf("failed to rename output file: %v", err)
		}
	}

	logrus.Infof("Conversion completed successfully: %s -> %s", inputPath, outputPath)
	return nil
}

// ValidateConversion 验证转换结果
func (c *ConverterService) ValidateConversion(outputPath, outputType string) error {
	// 检查文件是否存在
	info, err := os.Stat(outputPath)
	if err != nil {
		return fmt.Errorf("output file does not exist: %v", err)
	}

	// 检查文件大小
	if info.Size() == 0 {
		return fmt.Errorf("output file is empty")
	}

	// 根据输出类型进行不同的验证
	switch outputType {
	case "pdf":
		return c.validatePDF(outputPath)
	case "jpg", "png":
		return c.validateImage(outputPath, outputType)
	default:
		return fmt.Errorf("unsupported output type for validation: %s", outputType)
	}
}

// validatePDF 验证PDF文件
func (c *ConverterService) validatePDF(outputPath string) error {
	file, err := os.Open(outputPath)
	if err != nil {
		return fmt.Errorf("failed to open output file: %v", err)
	}
	defer file.Close()

	header := make([]byte, 4)
	_, err = file.Read(header)
	if err != nil {
		return fmt.Errorf("failed to read file header: %v", err)
	}

	if string(header) != "%PDF" {
		return fmt.Errorf("output file is not a valid PDF")
	}

	logrus.Infof("PDF validation passed: %s", outputPath)
	return nil
}

// validateImage 验证图片文件
func (c *ConverterService) validateImage(outputPath, imageFormat string) error {
	file, err := os.Open(outputPath)
	if err != nil {
		return fmt.Errorf("failed to open output file: %v", err)
	}
	defer file.Close()

	// 读取文件头进行简单验证
	header := make([]byte, 8)
	_, err = file.Read(header)
	if err != nil {
		return fmt.Errorf("failed to read file header: %v", err)
	}

	switch imageFormat {
	case "jpg":
		// JPEG文件头: FF D8 FF
		if header[0] != 0xFF || header[1] != 0xD8 || header[2] != 0xFF {
			return fmt.Errorf("output file is not a valid JPEG")
		}
	case "png":
		// PNG文件头: 89 50 4E 47 0D 0A 1A 0A
		pngHeader := []byte{0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A}
		for i, b := range pngHeader {
			if header[i] != b {
				return fmt.Errorf("output file is not a valid PNG")
			}
		}
	}

	logrus.Infof("Image validation passed: %s", outputPath)
	return nil
}

// ConvertToImage 将文件转换为图片
func (c *ConverterService) ConvertToImage(inputPath, outputPath, imageFormat string) error {
	// 首先转换为PDF
	tempPDFPath := strings.TrimSuffix(outputPath, filepath.Ext(outputPath)) + "_temp.pdf"
	defer os.Remove(tempPDFPath) // 清理临时PDF文件

	// 转换为PDF
	if err := c.ConvertToPDF(inputPath, tempPDFPath); err != nil {
		return fmt.Errorf("failed to convert to PDF: %v", err)
	}

	// 将PDF转换为图片
	return c.convertPDFToImage(tempPDFPath, outputPath, imageFormat)
}

// convertPDFToImage 将PDF转换为图片
func (c *ConverterService) convertPDFToImage(pdfPath, outputPath, imageFormat string) error {
	// 检查ImageMagick是否可用
	var convertCmd string
	possibleCommands := []string{"magick", "convert"}

	for _, cmd := range possibleCommands {
		if _, err := exec.LookPath(cmd); err == nil {
			convertCmd = cmd
			break
		}
	}

	if convertCmd == "" {
		return fmt.Errorf("ImageMagick not found. Please install ImageMagick")
	}

	// ImageMagick命令参数
	args := []string{
		"-density", "300",        // 设置DPI为300
		"-quality", "90",         // 设置质量为90%
		pdfPath + "[0]",         // 只转换第一页
		outputPath,
	}

	// 如果是PNG格式，添加透明背景支持
	if imageFormat == "png" {
		args = append([]string{"-background", "white", "-alpha", "remove"}, args...)
	}

	logrus.Infof("Executing ImageMagick conversion: %s %s", convertCmd, strings.Join(args, " "))

	// 创建带超时的命令
	cmd := exec.Command(convertCmd, args...)

	// 执行命令并设置超时
	done := make(chan error, 1)
	go func() {
		output, err := cmd.CombinedOutput()
		if err != nil {
			logrus.Errorf("ImageMagick conversion failed: %v, output: %s", err, string(output))
			done <- fmt.Errorf("image conversion failed: %v, output: %s", err, string(output))
		} else {
			logrus.Infof("ImageMagick conversion output: %s", string(output))
			done <- nil
		}
	}()

	// 等待完成或超时
	select {
	case err := <-done:
		if err != nil {
			return err
		}
	case <-time.After(3 * time.Minute): // 3分钟超时
		if cmd.Process != nil {
			cmd.Process.Kill()
		}
		return fmt.Errorf("image conversion timeout after 3 minutes")
	}

	// 检查输出文件是否存在
	if _, err := os.Stat(outputPath); os.IsNotExist(err) {
		return fmt.Errorf("image conversion completed but output file not found: %s", outputPath)
	}

	logrus.Infof("Image conversion completed successfully: %s -> %s", pdfPath, outputPath)
	return nil
}
