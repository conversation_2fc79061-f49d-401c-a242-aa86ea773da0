package utils

import (
	"fmt"
	"mime/multipart"
	"path/filepath"
	"strings"

	"docx2pdf/config"
)

// FileValidationError 文件验证错误
type FileValidationError struct {
	Code    string
	Message string
}

func (e *FileValidationError) Error() string {
	return e.Message
}

// ValidateFile 验证上传的文件
func ValidateFile(fileHeader *multipart.FileHeader) error {
	// 检查文件大小
	if fileHeader.Size > config.AppConfig.MaxFileSize {
		return &FileValidationError{
			Code:    "FILE_TOO_LARGE",
			Message: fmt.Sprintf("File size %d bytes exceeds limit of %d bytes", fileHeader.Size, config.AppConfig.MaxFileSize),
		}
	}
	
	// 检查文件类型
	ext := strings.ToLower(filepath.Ext(fileHeader.Filename))
	if !isAllowedFileType(ext) {
		return &FileValidationError{
			Code:    "INVALID_FILE_TYPE",
			Message: fmt.Sprintf("Unsupported file format '%s'. Only %v are allowed", ext, config.AppConfig.AllowedFileTypes),
		}
	}
	
	return nil
}

// isAllowedFileType 检查文件类型是否被允许
func isAllowedFileType(ext string) bool {
	for _, allowedExt := range config.AppConfig.AllowedFileTypes {
		if ext == allowedExt {
			return true
		}
	}
	return false
}

// GetFileType 根据文件扩展名获取文件类型
func GetFileType(filename string) string {
	ext := strings.ToLower(filepath.Ext(filename))
	switch ext {
	case ".docx", ".doc":
		return "word"
	case ".pptx", ".ppt":
		return "powerpoint"
	default:
		return "unknown"
	}
}

// GenerateOutputFilename 生成输出文件名
func GenerateOutputFilename(originalFilename string) string {
	ext := filepath.Ext(originalFilename)
	nameWithoutExt := strings.TrimSuffix(originalFilename, ext)
	return nameWithoutExt + ".pdf"
}

// GenerateOutputFilenameWithType 根据输出类型生成输出文件名
func GenerateOutputFilenameWithType(originalFilename, outputType string) string {
	ext := filepath.Ext(originalFilename)
	nameWithoutExt := strings.TrimSuffix(originalFilename, ext)
	return nameWithoutExt + "." + outputType
}

// IsValidOutputType 验证输出类型是否有效
func IsValidOutputType(outputType string) bool {
	validTypes := []string{"pdf", "jpg", "png"}
	for _, validType := range validTypes {
		if outputType == validType {
			return true
		}
	}
	return false
}
