.PHONY: help build run test clean docker-build docker-run docker-stop deps fmt lint

# 默认目标
help: ## 显示帮助信息
	@echo "可用的命令:"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-15s\033[0m %s\n", $$1, $$2}'

# 变量定义
APP_NAME := docx2pdf
VERSION := $(shell git describe --tags --always --dirty 2>/dev/null || echo "dev")
BUILD_TIME := $(shell date +%Y-%m-%d_%H:%M:%S)
GO_VERSION := $(shell go version | awk '{print $$3}')

# 构建标志
LDFLAGS := -ldflags "-X main.Version=$(VERSION) -X main.BuildTime=$(BUILD_TIME) -X main.GoVersion=$(GO_VERSION)"

# 开发相关命令
deps: ## 安装依赖
	go mod download
	go mod tidy

fmt: ## 格式化代码
	go fmt ./...

lint: ## 代码检查
	golangci-lint run

test: ## 运行测试
	go test -v ./...

test-coverage: ## 运行测试并生成覆盖率报告
	go test -coverprofile=coverage.out ./...
	go tool cover -html=coverage.out -o coverage.html
	@echo "覆盖率报告已生成: coverage.html"

# 构建相关命令
build: ## 构建应用
	CGO_ENABLED=1 go build $(LDFLAGS) -o $(APP_NAME) .

build-linux: ## 构建 Linux 版本
	CGO_ENABLED=1 GOOS=linux GOARCH=amd64 go build $(LDFLAGS) -o $(APP_NAME)-linux .

build-windows: ## 构建 Windows 版本
	CGO_ENABLED=1 GOOS=windows GOARCH=amd64 go build $(LDFLAGS) -o $(APP_NAME)-windows.exe .

build-all: build-linux build-windows ## 构建所有平台版本

# 运行相关命令
run: ## 运行应用
	go run . 

dev: ## 开发模式运行 (带热重载)
	air

# Docker 相关命令
docker-build: ## 构建 Docker 镜像
	docker build -t $(APP_NAME):$(VERSION) .
	docker tag $(APP_NAME):$(VERSION) $(APP_NAME):latest

docker-run: ## 运行 Docker 容器
	docker-compose up -d

docker-stop: ## 停止 Docker 容器
	docker-compose down

docker-logs: ## 查看 Docker 日志
	docker-compose logs -f

docker-clean: ## 清理 Docker 资源
	docker-compose down -v
	docker rmi $(APP_NAME):$(VERSION) $(APP_NAME):latest 2>/dev/null || true

# 部署相关命令
deploy-dev: ## 部署到开发环境
	docker-compose -f docker-compose.yml up -d

deploy-prod: ## 部署到生产环境
	docker-compose -f docker-compose.prod.yml up -d

# 数据库相关命令
db-migrate: ## 运行数据库迁移
	@echo "数据库迁移功能待实现"

db-backup: ## 备份数据库
	@if [ -f "./data/docx2pdf.db" ]; then \
		cp ./data/docx2pdf.db ./data/docx2pdf_backup_$(shell date +%Y%m%d_%H%M%S).db; \
		echo "数据库备份完成"; \
	else \
		echo "数据库文件不存在"; \
	fi

# 清理相关命令
clean: ## 清理构建文件
	rm -f $(APP_NAME) $(APP_NAME)-linux $(APP_NAME)-windows.exe
	rm -f coverage.out coverage.html
	rm -rf temp/*
	go clean

clean-all: clean docker-clean ## 清理所有文件

# 工具相关命令
install-tools: ## 安装开发工具
	go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
	go install github.com/cosmtrek/air@latest

setup: deps install-tools ## 初始化开发环境
	@echo "开发环境设置完成"

# 健康检查
health: ## 检查服务健康状态
	@curl -s http://localhost:48084/health | jq . || echo "服务未运行或健康检查失败"

# 版本信息
version: ## 显示版本信息
	@echo "应用名称: $(APP_NAME)"
	@echo "版本: $(VERSION)"
	@echo "构建时间: $(BUILD_TIME)"
	@echo "Go 版本: $(GO_VERSION)"

# 生成文档
docs: ## 生成 API 文档
	@echo "API 文档生成功能待实现"

# 安全检查
security: ## 运行安全检查
	gosec ./...

# 性能测试
benchmark: ## 运行性能测试
	go test -bench=. -benchmem ./...

# 发布相关
release: build-all ## 准备发布
	@echo "准备发布版本: $(VERSION)"
	@mkdir -p release
	@cp $(APP_NAME)-linux release/
	@cp $(APP_NAME)-windows.exe release/
	@cp README.md release/
	@cp .env.example release/
	@echo "发布文件已准备完成，位于 release/ 目录"
