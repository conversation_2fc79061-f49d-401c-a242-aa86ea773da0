package services

import (
	"fmt"
	"os"
	"path/filepath"

	"github.com/aliyun/aliyun-oss-go-sdk/oss"
	"github.com/sirupsen/logrus"

	"docx2pdf/config"
)

type StorageService struct {
	client *oss.Client
	bucket *oss.Bucket
}

var Storage *StorageService

// InitStorage 初始化阿里云OSS存储服务
func InitStorage() error {
	client, err := oss.New(
		config.AppConfig.OSSEndpoint,
		config.AppConfig.OSSAccessKeyID,
		config.AppConfig.OSSAccessKeySecret,
	)
	if err != nil {
		return fmt.Errorf("failed to create OSS client: %v", err)
	}
	
	bucket, err := client.Bucket(config.AppConfig.OSSBucketName)
	if err != nil {
		return fmt.Errorf("failed to get OSS bucket: %v", err)
	}
	
	Storage = &StorageService{
		client: client,
		bucket: bucket,
	}
	
	logrus.Info("OSS storage service initialized successfully")
	return nil
}

// UploadFile 上传文件到OSS
func (s *StorageService) UploadFile(localFilePath, objectKey string) (string, error) {
	// 检查本地文件是否存在
	if _, err := os.Stat(localFilePath); os.IsNotExist(err) {
		return "", fmt.Errorf("local file does not exist: %s", localFilePath)
	}
	
	// 上传文件
	err := s.bucket.PutObjectFromFile(objectKey, localFilePath)
	if err != nil {
		return "", fmt.Errorf("failed to upload file to OSS: %v", err)
	}
	
	// 生成访问URL
	url := fmt.Sprintf("https://%s.%s/%s", 
		config.AppConfig.OSSBucketName, 
		config.AppConfig.OSSEndpoint, 
		objectKey)
	
	logrus.Infof("File uploaded successfully: %s -> %s", localFilePath, url)
	return url, nil
}

// GenerateObjectKey 生成OSS对象键
func (s *StorageService) GenerateObjectKey(taskID, filename string) string {
	return fmt.Sprintf("pdfs/%s/%s", taskID, filename)
}

// DeleteFile 删除OSS文件
func (s *StorageService) DeleteFile(objectKey string) error {
	err := s.bucket.DeleteObject(objectKey)
	if err != nil {
		return fmt.Errorf("failed to delete file from OSS: %v", err)
	}
	
	logrus.Infof("File deleted from OSS: %s", objectKey)
	return nil
}

// SaveTempFile 保存临时文件
func SaveTempFile(taskID, filename string, data []byte) (string, error) {
	// 确保临时目录存在
	tempDir := config.AppConfig.TempDir
	if err := os.MkdirAll(tempDir, 0755); err != nil {
		return "", fmt.Errorf("failed to create temp directory: %v", err)
	}
	
	// 创建任务专用目录
	taskDir := filepath.Join(tempDir, taskID)
	if err := os.MkdirAll(taskDir, 0755); err != nil {
		return "", fmt.Errorf("failed to create task directory: %v", err)
	}
	
	// 保存文件
	filePath := filepath.Join(taskDir, filename)
	if err := os.WriteFile(filePath, data, 0644); err != nil {
		return "", fmt.Errorf("failed to save temp file: %v", err)
	}
	
	logrus.Infof("Temp file saved: %s", filePath)
	return filePath, nil
}

// CleanupTempFile 清理临时文件
func CleanupTempFile(filePath string) {
	if err := os.Remove(filePath); err != nil {
		logrus.Warnf("Failed to cleanup temp file %s: %v", filePath, err)
	} else {
		logrus.Infof("Temp file cleaned up: %s", filePath)
	}
	
	// 尝试删除空的任务目录
	dir := filepath.Dir(filePath)
	if err := os.Remove(dir); err != nil {
		// 目录可能不为空，这是正常的
		logrus.Debugf("Could not remove directory %s: %v", dir, err)
	}
}
