package main

import (
	"fmt"
	"os"
	"os/signal"
	"syscall"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"

	"docx2pdf/config"
	"docx2pdf/handlers"
	"docx2pdf/models"
	"docx2pdf/services"
	"docx2pdf/worker"
)

func main() {
	// 设置日志格式
	logrus.SetFormatter(&logrus.TextFormatter{
		FullTimestamp: true,
	})
	logrus.SetLevel(logrus.InfoLevel)
	
	// 加载配置
	if err := config.LoadConfig(); err != nil {
		logrus.Fatalf("Failed to load config: %v", err)
	}
	
	// 初始化数据库
	db, err := initDatabase()
	if err != nil {
		logrus.Fatalf("Failed to initialize database: %v", err)
	}
	
	// 初始化服务
	if err := initServices(); err != nil {
		logrus.Fatalf("Failed to initialize services: %v", err)
	}
	
	// 启动Worker
	worker.StartWorkers(db, 2) // 启动2个Worker
	
	// 设置Gin模式
	gin.SetMode(gin.ReleaseMode)
	
	// 创建路由
	router := setupRouter(db)
	
	// 启动服务器
	port := config.AppConfig.ServerPort
	logrus.Infof("Starting server on port %s", port)
	
	// 优雅关闭
	go func() {
		if err := router.Run(":" + port); err != nil {
			logrus.Fatalf("Failed to start server: %v", err)
		}
	}()
	
	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	
	logrus.Info("Shutting down server...")
}

// initDatabase 初始化数据库
func initDatabase() (*gorm.DB, error) {
	db, err := gorm.Open(sqlite.Open(config.AppConfig.DatabasePath), &gorm.Config{})
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %v", err)
	}
	
	// 自动迁移
	if err := db.AutoMigrate(&models.Task{}); err != nil {
		return nil, fmt.Errorf("failed to migrate database: %v", err)
	}
	
	logrus.Info("Database initialized successfully")
	return db, nil
}

// initServices 初始化服务
func initServices() error {
	// 初始化Redis队列
	if err := services.InitQueue(); err != nil {
		return fmt.Errorf("failed to initialize queue: %v", err)
	}
	
	// 初始化OSS存储
	if err := services.InitStorage(); err != nil {
		return fmt.Errorf("failed to initialize storage: %v", err)
	}
	
	// 初始化转换器
	services.InitConverter()
	
	return nil
}

// setupRouter 设置路由
func setupRouter(db *gorm.DB) *gin.Engine {
	router := gin.New()
	
	// 中间件
	router.Use(gin.Logger())
	router.Use(gin.Recovery())
	
	// CORS配置
	router.Use(cors.New(cors.Config{
		AllowOrigins:     []string{"*"},
		AllowMethods:     []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowHeaders:     []string{"*"},
		ExposeHeaders:    []string{"Content-Length"},
		AllowCredentials: true,
	}))
	
	// 创建处理器
	convertHandler := handlers.NewConvertHandler(db)
	taskHandler := handlers.NewTaskHandler(db)
	
	// API路由
	v1 := router.Group("/api/v1")
	{
		// 文件转换
		v1.POST("/convert", convertHandler.ConvertFile)
		
		// 任务管理
		v1.GET("/tasks/:task_id", taskHandler.GetTaskStatus)
		v1.GET("/tasks", taskHandler.ListTasks) // 可选的任务列表接口
	}
	
	// 健康检查
	router.GET("/health", func(c *gin.Context) {
		// 检查Redis连接
		queueLength, err := services.Queue.GetQueueLength()
		if err != nil {
			c.JSON(500, gin.H{
				"status": "unhealthy",
				"error":  "Redis connection failed",
			})
			return
		}
		
		c.JSON(200, gin.H{
			"status":       "healthy",
			"queue_length": queueLength,
		})
	})
	
	// 根路径
	router.GET("/", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"service": "docx2pdf",
			"version": "1.0.1",
			"status":  "running",
		})
	})
	
	return router
}
