# Git
.git
.gitignore

# Documentation
README.md
docs/
*.md

# Environment files
.env
.env.local
.env.*.local

# Temporary files
temp/
*.tmp
*.temp

# Data files
data/
*.db
*.sqlite

# Logs
*.log
logs/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# Build artifacts
docx2pdf
*.exe
*.dll
*.so
*.dylib

# Test files
*_test.go
test/
coverage.out
coverage.html

# Docker files
Dockerfile*
docker-compose*.yml
.dockerignore
