# 文档转图片依赖安装脚本 (Windows PowerShell)
# 需要管理员权限运行

param(
    [switch]$Force = $false
)

# 检查管理员权限
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "此脚本需要管理员权限运行。请以管理员身份重新运行 PowerShell。" -ForegroundColor Red
    exit 1
}

Write-Host "=== 文档转图片服务依赖安装脚本 (Windows) ===" -ForegroundColor Green
Write-Host "此脚本将安装以下依赖："
Write-Host "1. Chocolatey (包管理器)"
Write-Host "2. LibreOffice (文档转换)"
Write-Host "3. ImageMagick (PDF转图片)"
Write-Host ""

# 检查并安装 Chocolatey
function Install-Chocolatey {
    if (Get-Command choco -ErrorAction SilentlyContinue) {
        Write-Host "✓ Chocolatey 已安装" -ForegroundColor Green
    } else {
        Write-Host "安装 Chocolatey..." -ForegroundColor Yellow
        Set-ExecutionPolicy Bypass -Scope Process -Force
        [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
        iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
        
        if (Get-Command choco -ErrorAction SilentlyContinue) {
            Write-Host "✓ Chocolatey 安装成功" -ForegroundColor Green
        } else {
            Write-Host "✗ Chocolatey 安装失败" -ForegroundColor Red
            exit 1
        }
    }
}

# 安装 LibreOffice
function Install-LibreOffice {
    Write-Host "检查 LibreOffice..." -ForegroundColor Yellow
    
    $libreOfficePaths = @(
        "C:\Program Files\LibreOffice\program\soffice.exe",
        "C:\Program Files (x86)\LibreOffice\program\soffice.exe"
    )
    
    $libreOfficeInstalled = $false
    foreach ($path in $libreOfficePaths) {
        if (Test-Path $path) {
            Write-Host "✓ LibreOffice 已安装: $path" -ForegroundColor Green
            $libreOfficeInstalled = $true
            break
        }
    }
    
    if (-not $libreOfficeInstalled -or $Force) {
        Write-Host "安装 LibreOffice..." -ForegroundColor Yellow
        choco install libreoffice -y
        
        # 验证安装
        $installed = $false
        foreach ($path in $libreOfficePaths) {
            if (Test-Path $path) {
                Write-Host "✓ LibreOffice 安装成功: $path" -ForegroundColor Green
                $installed = $true
                break
            }
        }
        
        if (-not $installed) {
            Write-Host "✗ LibreOffice 安装失败" -ForegroundColor Red
        }
    }
}

# 安装 ImageMagick
function Install-ImageMagick {
    Write-Host "检查 ImageMagick..." -ForegroundColor Yellow
    
    if (Get-Command magick -ErrorAction SilentlyContinue) {
        Write-Host "✓ ImageMagick 已安装" -ForegroundColor Green
    } elseif (-not (Get-Command magick -ErrorAction SilentlyContinue) -or $Force) {
        Write-Host "安装 ImageMagick..." -ForegroundColor Yellow
        choco install imagemagick -y
        
        # 刷新环境变量
        $env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")
        
        if (Get-Command magick -ErrorAction SilentlyContinue) {
            Write-Host "✓ ImageMagick 安装成功" -ForegroundColor Green
        } else {
            Write-Host "✗ ImageMagick 安装失败" -ForegroundColor Red
        }
    }
}

# 主安装流程
try {
    Install-Chocolatey
    Install-LibreOffice
    Install-ImageMagick
    
    Write-Host ""
    Write-Host "=== 验证安装 ===" -ForegroundColor Green
    
    # 验证 LibreOffice
    $libreOfficePaths = @(
        "C:\Program Files\LibreOffice\program\soffice.exe",
        "C:\Program Files (x86)\LibreOffice\program\soffice.exe"
    )
    
    $libreOfficeFound = $false
    foreach ($path in $libreOfficePaths) {
        if (Test-Path $path) {
            Write-Host "✓ LibreOffice: $path" -ForegroundColor Green
            $libreOfficeFound = $true
            break
        }
    }
    
    if (-not $libreOfficeFound) {
        Write-Host "✗ LibreOffice 未找到" -ForegroundColor Red
    }
    
    # 验证 ImageMagick
    if (Get-Command magick -ErrorAction SilentlyContinue) {
        $version = & magick --version | Select-Object -First 1
        Write-Host "✓ ImageMagick: $version" -ForegroundColor Green
    } else {
        Write-Host "✗ ImageMagick 未找到" -ForegroundColor Red
    }
    
    Write-Host ""
    Write-Host "=== 安装完成 ===" -ForegroundColor Green
    Write-Host "现在可以使用文档转图片功能了！"
    Write-Host ""
    Write-Host "使用示例："
    Write-Host 'curl -X POST http://localhost:48084/api/v1/convert \' -ForegroundColor Cyan
    Write-Host '  -F "files=@document.docx" \' -ForegroundColor Cyan
    Write-Host '  -F "outType=jpg"' -ForegroundColor Cyan
    
} catch {
    Write-Host "安装过程中出现错误: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "注意事项："
Write-Host "1. 如果遇到权限问题，请确保以管理员身份运行此脚本"
Write-Host "2. 安装完成后可能需要重启终端或重新登录以刷新环境变量"
Write-Host "3. 如果 ImageMagick 命令不可用，请手动添加到 PATH 环境变量"
