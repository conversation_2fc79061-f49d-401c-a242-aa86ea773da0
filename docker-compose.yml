version: "3.8"

services:
  docx2pdf:
    build: .
    ports:
      - "48084:48084"
    environment:
      - REDIS_ADDR=redis:6379
      - OSS_ENDPOINT=${OSS_ENDPOINT}
      - OSS_ACCESS_KEY_ID=${OSS_ACCESS_KEY_ID}
      - OSS_ACCESS_KEY_SECRET=${OSS_ACCESS_KEY_SECRET}
      - OSS_BUCKET_NAME=${OSS_BUCKET_NAME}
      - MAX_FILE_SIZE=${MAX_FILE_SIZE:-524288000}
      - TEMP_DIR=/app/temp
      - DATABASE_PATH=/app/data/docx2pdf.db
    volumes:
      - ./temp:/app/temp
      - ./data:/app/data
    depends_on:
      - redis
    restart: unless-stopped
    healthcheck:
      test:
        [
          "CMD",
          "wget",
          "--no-verbose",
          "--tries=1",
          "--spider",
          "http://localhost:48084/health",
        ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  redis:
    image: redis:7-alpine
    ports:
      - "6479:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 3s
      retries: 3

volumes:
  redis_data:
