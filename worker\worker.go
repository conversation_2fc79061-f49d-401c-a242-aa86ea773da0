package worker

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"

	"docx2pdf/models"
	"docx2pdf/services"
	"docx2pdf/utils"
)

type Worker struct {
	db       *gorm.DB
	workerID string
}

func NewWorker(db *gorm.DB, workerID string) *Worker {
	return &Worker{
		db:       db,
		workerID: workerID,
	}
}

// Start 启动Worker
func (w *Worker) Start() {
	logrus.Infof("Worker %s started", w.workerID)
	
	for {
		// 从队列中获取任务
		taskMsg, err := services.Queue.DequeueTask(30 * time.Second)
		if err != nil {
			logrus.Errorf("Worker %s failed to dequeue task: %v", w.workerID, err)
			time.Sleep(5 * time.Second)
			continue
		}
		
		if taskMsg == nil {
			// 没有任务，继续等待
			continue
		}
		
		// 处理任务
		w.processTask(taskMsg)
	}
}

// processTask 处理单个任务
func (w *Worker) processTask(taskMsg *services.TaskMessage) {
	logrus.Infof("Worker %s processing task: %s", w.workerID, taskMsg.TaskID)
	
	// 从数据库获取任务详情
	var task models.Task
	err := w.db.Where("task_id = ?", taskMsg.TaskID).First(&task).Error
	if err != nil {
		logrus.Errorf("Worker %s failed to find task %s: %v", w.workerID, taskMsg.TaskID, err)
		return
	}
	
	// 更新任务状态为处理中
	outputType := task.OutputType
	if outputType == "" {
		outputType = "pdf"
	}
	statusMessage := fmt.Sprintf("Converting file to %s...", strings.ToUpper(outputType))
	if err := task.UpdateStatus(w.db, models.StatusProcessing, statusMessage); err != nil {
		logrus.Errorf("Worker %s failed to update task status: %v", w.workerID, err)
		return
	}
	
	// 执行转换
	success := w.convertFile(&task)
	
	// 清理临时文件
	if task.TempFilePath != "" {
		services.CleanupTempFile(task.TempFilePath)
	}
	
	if success {
		logrus.Infof("Worker %s completed task: %s", w.workerID, taskMsg.TaskID)
	} else {
		logrus.Errorf("Worker %s failed task: %s", w.workerID, taskMsg.TaskID)
	}
}

// convertFile 执行文件转换
func (w *Worker) convertFile(task *models.Task) bool {
	// 检查临时文件是否存在
	if _, err := os.Stat(task.TempFilePath); os.IsNotExist(err) {
		task.SetError(w.db, "FILE_NOT_FOUND", "Temporary file not found")
		return false
	}
	
	// 生成输出文件路径
	outputType := task.OutputType
	if outputType == "" {
		outputType = "pdf" // 默认为PDF
	}
	outputFilename := utils.GenerateOutputFilenameWithType(task.OriginalFilename, outputType)
	outputDir := filepath.Join(filepath.Dir(task.TempFilePath), "output")
	outputPath := filepath.Join(outputDir, outputFilename)

	// 执行转换
	err := services.Converter.ConvertFile(task.TempFilePath, outputPath, outputType)
	if err != nil {
		logrus.Errorf("Conversion failed for task %s: %v", task.TaskID, err)
		task.SetError(w.db, "CONVERSION_ERROR", fmt.Sprintf("Conversion failed: %v", err))
		return false
	}

	// 验证转换结果
	if err := services.Converter.ValidateConversion(outputPath, outputType); err != nil {
		logrus.Errorf("Conversion validation failed for task %s: %v", task.TaskID, err)
		task.SetError(w.db, "VALIDATION_ERROR", fmt.Sprintf("Conversion validation failed: %v", err))
		return false
	}
	
	// 处理文件上传（支持多页图片）
	if err := w.uploadOutputFiles(task, outputPath, outputFilename, outputType); err != nil {
		logrus.Errorf("Failed to upload files for task %s: %v", task.TaskID, err)
		task.SetError(w.db, "UPLOAD_ERROR", fmt.Sprintf("Failed to upload files: %v", err))
		return false
	}
	
	if err := task.UpdateStatus(w.db, models.StatusCompleted, "Conversion completed successfully"); err != nil {
		logrus.Errorf("Failed to update task completion status: %v", err)
		return false
	}
	
	// 清理本地输出文件
	if err := os.Remove(outputPath); err != nil {
		logrus.Warnf("Failed to cleanup output file %s: %v", outputPath, err)
	}
	
	return true
}

// StartWorkers 启动多个Worker
func StartWorkers(db *gorm.DB, workerCount int) {
	for i := 0; i < workerCount; i++ {
		workerID := fmt.Sprintf("worker-%d", i+1)
		worker := NewWorker(db, workerID)
		
		go worker.Start()
	}
	
	logrus.Infof("Started %d workers", workerCount)
}

// uploadOutputFiles 上传输出文件（支持多页图片）
func (w *Worker) uploadOutputFiles(task *models.Task, outputPath, outputFilename, outputType string) error {
	outputDir := filepath.Dir(outputPath)
	outputBaseName := strings.TrimSuffix(filepath.Base(outputPath), filepath.Ext(outputPath))

	// 检查是否有多页文件（图片格式）
	if outputType == "jpg" || outputType == "png" {
		// 查找所有页面文件
		pagePattern := filepath.Join(outputDir, fmt.Sprintf("%s_page_*.%s", outputBaseName, outputType))
		pageFiles, err := filepath.Glob(pagePattern)
		if err != nil {
			return fmt.Errorf("failed to search for page files: %v", err)
		}

		if len(pageFiles) > 0 {
			// 有页面文件（单页或多页）
			return w.uploadMultiplePages(task, pageFiles, outputPath, outputFilename, outputType)
		}
	}

	// 单页情况
	return w.uploadSingleFile(task, outputPath, outputFilename)
}

// uploadSingleFile 上传单个文件
func (w *Worker) uploadSingleFile(task *models.Task, outputPath, outputFilename string) error {
	// 上传到OSS
	objectKey := services.Storage.GenerateObjectKey(task.TaskID, outputFilename)
	outputURL, err := services.Storage.UploadFile(outputPath, objectKey)
	if err != nil {
		return fmt.Errorf("failed to upload file: %v", err)
	}

	// 获取输出文件大小
	fileInfo, err := os.Stat(outputPath)
	if err != nil {
		logrus.Warnf("Failed to get output file size for task %s: %v", task.TaskID, err)
	} else {
		task.FileSize = fileInfo.Size()
	}

	// 更新任务信息
	task.ConvertedFilename = outputFilename
	task.OutputFilePath = outputPath
	task.OutputURL = outputURL
	task.PageCount = 1

	return nil
}

// uploadMultiplePages 上传多页文件
func (w *Worker) uploadMultiplePages(task *models.Task, pageFiles []string, mainOutputPath, outputFilename, outputType string) error {
	var urls []string
	var totalSize int64

	// 对页面文件进行排序 (按页码数字排序)
	sort.Slice(pageFiles, func(i, j int) bool {
		// 提取页码进行数字排序
		nameI := filepath.Base(pageFiles[i])
		nameJ := filepath.Base(pageFiles[j])

		// 使用简单的字符串比较，因为文件名已经是标准格式
		return nameI < nameJ
	})

	// 上传每个页面文件
	for i, pageFile := range pageFiles {
		pageFilename := filepath.Base(pageFile)
		objectKey := services.Storage.GenerateObjectKey(task.TaskID, pageFilename)

		pageURL, err := services.Storage.UploadFile(pageFile, objectKey)
		if err != nil {
			return fmt.Errorf("failed to upload page %d: %v", i+1, err)
		}

		urls = append(urls, pageURL)

		// 累计文件大小
		if fileInfo, err := os.Stat(pageFile); err == nil {
			totalSize += fileInfo.Size()
		}

		// 清理页面文件
		if err := os.Remove(pageFile); err != nil {
			logrus.Warnf("Failed to cleanup page file %s: %v", pageFile, err)
		}

		logrus.Infof("Uploaded page %d for task %s: %s", i+1, task.TaskID, pageURL)
	}

	// 序列化URLs
	urlsJSON, err := json.Marshal(urls)
	if err != nil {
		return fmt.Errorf("failed to marshal URLs: %v", err)
	}

	// 更新任务信息
	task.ConvertedFilename = outputFilename
	task.OutputFilePath = mainOutputPath
	task.OutputURL = urls[0] // 主URL为第一页
	task.OutputURLs = string(urlsJSON)
	task.PageCount = len(urls)
	task.FileSize = totalSize

	logrus.Infof("Uploaded %d pages for task %s", len(urls), task.TaskID)
	return nil
}
